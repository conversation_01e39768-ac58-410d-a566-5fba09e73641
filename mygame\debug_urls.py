#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试URL配置
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

django.setup()

from django.urls import reverse, resolve
from django.test import Client
from django.conf import settings

def test_url_resolution():
    """测试URL解析"""
    print("🔍 测试URL解析...")
    
    try:
        # 测试reverse解析
        print("\n1. 测试reverse解析:")
        try:
            url = reverse('simple_test')
            print(f"   ✅ simple_test URL: {url}")
        except Exception as e:
            print(f"   ❌ simple_test reverse失败: {e}")
        
        # 测试resolve解析
        print("\n2. 测试resolve解析:")
        try:
            match = resolve('/simple-test/')
            print(f"   ✅ /simple-test/ 解析成功: {match.func}")
            print(f"   视图名称: {match.view_name}")
            print(f"   URL名称: {match.url_name}")
        except Exception as e:
            print(f"   ❌ /simple-test/ resolve失败: {e}")
        
        # 测试Client请求
        print("\n3. 测试Client请求:")
        client = Client()
        try:
            response = client.get('/simple-test/')
            print(f"   ✅ GET /simple-test/ 状态码: {response.status_code}")
            if response.status_code == 200:
                print(f"   响应内容长度: {len(response.content)} bytes")
                if "简单测试页面".encode('utf-8') in response.content:
                    print("   ✅ 页面内容正确")
                else:
                    print("   ⚠️ 页面内容可能不正确")
            else:
                print(f"   ❌ 响应状态码不是200: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Client请求失败: {e}")
        
        # 列出所有URL模式
        print("\n4. 列出相关URL模式:")
        from django.urls import get_resolver
        resolver = get_resolver()
        
        def print_urls(urlpatterns, prefix=''):
            for pattern in urlpatterns:
                if hasattr(pattern, 'url_patterns'):
                    # 这是一个include()
                    print(f"   {prefix}{pattern.pattern} -> include")
                    print_urls(pattern.url_patterns, prefix + '  ')
                else:
                    # 这是一个普通的URL模式
                    print(f"   {prefix}{pattern.pattern} -> {pattern.callback}")
        
        print("   URL模式:")
        print_urls(resolver.url_patterns[:10])  # 只显示前10个避免太长
        
    except Exception as e:
        print(f"❌ URL测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_view_import():
    """测试视图导入"""
    print("\n🔍 测试视图导入...")
    
    try:
        from web.website.views.simple_test import simple_test_page
        print("   ✅ simple_test_page 导入成功")

        from web.website.views.xiuxian_test import xiuxian_test_page
        print("   ✅ xiuxian_test_page 导入成功")
        
        # 测试视图函数调用
        from django.http import HttpRequest
        request = HttpRequest()
        request.method = 'GET'
        
        response = simple_test_page(request)
        print(f"   ✅ 视图函数调用成功，状态码: {response.status_code}")
        print(f"   响应内容长度: {len(response.content)} bytes")
        
    except Exception as e:
        print(f"   ❌ 视图导入或调用失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🧙‍♂️ Django URL调试工具")
    print("="*50)
    
    print(f"Django版本: {django.get_version()}")
    print(f"设置模块: {settings.SETTINGS_MODULE}")
    print(f"DEBUG模式: {settings.DEBUG}")
    
    test_view_import()
    test_url_resolution()
    
    print("\n" + "="*50)
    print("🏁 调试完成")

if __name__ == "__main__":
    main()
