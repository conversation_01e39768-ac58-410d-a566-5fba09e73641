/* Evennia CSS styles for the game website */


/* Old site styles, left in just in case.
   Shouldn't break anything! */
@media (max-width: 570px) {
    .oneThird {
        width: 100%;
        border-left: none;
    }

    .quarter {
        width: 100%;
        border-left: none;
    }

    .headerTitle {
        text-align: right;
    }

    .headerSubTitle {
        text-align: right;
    }

    .headerLinks {
        position: static;
    }
}

/* If you for some reason need to override the default sizing model,
   for instance, if you're using a Google Maps widget, you may need
   to use the following code, see:
   http://v4-alpha.getbootstrap.com/getting-started/introduction/#box-sizing */
/*
.selector-for-some-widget {
  -webkit-box-sizing: content-box;
     -moz-box-sizing: content-box;
          box-sizing: content-box;
}
*/

/* Sticky footer styles
-------------------------------------------------- */
html {
  position: relative;
  min-height: 100%;
}

body {
  /* Margin bottom by footer height plus padding against main content*/
  margin-bottom: 92px;
  background-color: #eee;
}

.navbar-brand-logo {
    height: 64px;
    width: 64px;
}

/* Default Colors */

.navbar {
  background-color: #3d5c7a;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  /* Set the fixed height of the footer here */
  height: 60px;
  line-height: 60px; /* Vertically center the text there */
  background-color: #3d5c7a;
}


/* Fancy play - button */

a.playbutton {
	box-shadow: 0px 1px 10px 5px #9fb4f2;
	background:linear-gradient(to bottom, #7892c2 5%, #476e9e 100%);
	background-color:#7892c2;
	border-radius:12px;
	border:1px solid #4e6096;
	display:inline-block;
	cursor:pointer;
	color:#ffffff;
	font-family:Arial;
	font-size:19px;
	padding:14px 37px;
	text-decoration:none;
	text-shadow:0px 1px 0px #283966;
}
a.playbutton:hover {
	background:linear-gradient(to bottom, #476e9e 5%, #7892c2 100%);
	background-color:#476e9e;
}
a.playbutton:active {
	position:relative;
	top:1px;
}
