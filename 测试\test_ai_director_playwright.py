#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统Web界面Playwright测试
测试AI导演功能在Web界面的集成
"""

import asyncio
import json
import time
from playwright.async_api import async_playwright, expect


async def test_ai_director_web_interface():
    """测试AI导演Web界面功能"""
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context(viewport={'width': 1280, 'height': 720})
        page = await context.new_page()
        
        # 记录测试结果
        test_results = []
        
        try:
            print("🧪 AI导演Web界面测试开始")
            print("="*60)
            
            # 测试1: 访问游戏主页
            print("\n测试1: 访问游戏主页")
            try:
                await page.goto("http://localhost:4001", wait_until='networkidle')
                await page.wait_for_timeout(2000)
                
                # 检查页面标题
                title = await page.title()
                print(f"✅ 页面标题: {title}")
                test_results.append(("访问主页", True))
            except Exception as e:
                print(f"❌ 访问主页失败: {e}")
                test_results.append(("访问主页", False))
            
            # 测试2: 登录游戏
            print("\n测试2: 登录游戏")
            try:
                # 查找登录表单
                await page.wait_for_selector('input[name="username"]', timeout=5000)
                await page.fill('input[name="username"]', 'testuser')
                await page.fill('input[name="password"]', 'testpass123')
                
                # 点击登录按钮
                await page.click('button[type="submit"]')
                await page.wait_for_timeout(2000)
                
                # 检查是否登录成功（通过URL变化或特定元素）
                current_url = page.url
                if 'webclient' in current_url or 'game' in current_url:
                    print("✅ 登录成功")
                    test_results.append(("登录游戏", True))
                else:
                    # 如果没有跳转，尝试查找游戏界面元素
                    game_element = await page.query_selector('.game-container, #game-view, .webclient')
                    if game_element:
                        print("✅ 进入游戏界面")
                        test_results.append(("登录游戏", True))
                    else:
                        print("⚠️ 登录后未找到游戏界面")
                        test_results.append(("登录游戏", False))
                        
            except Exception as e:
                print(f"❌ 登录失败: {e}")
                test_results.append(("登录游戏", False))
            
            # 测试3: 触发AI导演事件
            print("\n测试3: 触发AI导演事件")
            try:
                # 等待游戏加载
                await page.wait_for_timeout(3000)
                
                # 尝试执行命令来触发事件
                # 查找命令输入框
                command_input = await page.query_selector('input[type="text"], .command-input, #input')
                
                if command_input:
                    # 输入测试命令
                    await command_input.fill('修炼')
                    await page.keyboard.press('Enter')
                    await page.wait_for_timeout(2000)
                    
                    # 查看是否有AI响应
                    # 检查输出区域
                    output_area = await page.query_selector('.output, #output, .message-area')
                    if output_area:
                        output_text = await output_area.inner_text()
                        if '修炼' in output_text or '灵气' in output_text:
                            print("✅ 检测到游戏响应")
                            test_results.append(("触发事件", True))
                        else:
                            print("⚠️ 未检测到预期响应")
                            test_results.append(("触发事件", False))
                    else:
                        print("⚠️ 未找到输出区域")
                        test_results.append(("触发事件", False))
                else:
                    print("⚠️ 未找到命令输入框")
                    test_results.append(("触发事件", False))
                    
            except Exception as e:
                print(f"❌ 触发事件失败: {e}")
                test_results.append(("触发事件", False))
            
            # 测试4: 检查AI导演状态面板
            print("\n测试4: 检查AI导演状态")
            try:
                # 尝试打开状态面板或执行状态命令
                command_input = await page.query_selector('input[type="text"], .command-input, #input')
                
                if command_input:
                    await command_input.fill('状态')
                    await page.keyboard.press('Enter')
                    await page.wait_for_timeout(2000)
                    
                    # 检查是否显示了状态信息
                    status_displayed = False
                    
                    # 方法1: 检查输出文本
                    output_area = await page.query_selector('.output, #output, .message-area')
                    if output_area:
                        output_text = await output_area.inner_text()
                        if any(keyword in output_text for keyword in ['境界', '等级', '修为', '状态']):
                            status_displayed = True
                    
                    # 方法2: 检查状态面板
                    status_panel = await page.query_selector('.status-panel, .character-status, #status')
                    if status_panel:
                        status_displayed = True
                    
                    if status_displayed:
                        print("✅ 成功显示状态信息")
                        test_results.append(("状态显示", True))
                    else:
                        print("⚠️ 未能显示状态信息")
                        test_results.append(("状态显示", False))
                else:
                    print("⚠️ 未找到命令输入框")
                    test_results.append(("状态显示", False))
                    
            except Exception as e:
                print(f"❌ 检查状态失败: {e}")
                test_results.append(("状态显示", False))
            
            # 测试5: 性能测试 - 快速连续触发多个事件
            print("\n测试5: 性能测试 - 连续事件响应")
            try:
                command_input = await page.query_selector('input[type="text"], .command-input, #input')
                
                if command_input:
                    response_times = []
                    
                    for i in range(5):
                        start_time = time.time()
                        
                        # 发送命令
                        await command_input.fill(f'测试命令{i}')
                        await page.keyboard.press('Enter')
                        
                        # 等待响应（检查输出变化）
                        await page.wait_for_timeout(200)  # 等待200ms
                        
                        response_time = time.time() - start_time
                        response_times.append(response_time)
                        
                        print(f"   命令{i+1}响应时间: {response_time*1000:.1f}ms")
                    
                    avg_response = sum(response_times) / len(response_times)
                    print(f"✅ 平均响应时间: {avg_response*1000:.1f}ms")
                    
                    # 检查是否满足<200ms的要求
                    if avg_response < 0.2:
                        test_results.append(("性能测试", True))
                    else:
                        test_results.append(("性能测试", False))
                else:
                    print("⚠️ 未找到命令输入框")
                    test_results.append(("性能测试", False))
                    
            except Exception as e:
                print(f"❌ 性能测试失败: {e}")
                test_results.append(("性能测试", False))
            
            # 截图保存测试结果
            await page.screenshot(path='ai_director_test_result.png')
            print("\n📸 已保存测试截图: ai_director_test_result.png")
            
        finally:
            await browser.close()
        
        # 输出测试总结
        print("\n" + "="*60)
        print("测试总结")
        print("="*60)
        
        passed = sum(1 for _, result in test_results if result)
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        
        print(f"\n总计: {passed}/{total} 测试通过")
        
        if passed == total:
            print("\n🎉 所有测试通过!")
        else:
            print(f"\n⚠️ 有 {total - passed} 个测试失败")
        
        return passed == total


async def test_ai_director_api():
    """测试AI导演API端点"""
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        print("\n🧪 AI导演API测试")
        print("="*60)
        
        api_results = []
        
        try:
            # 测试API端点（如果存在）
            api_endpoints = [
                ("/api/ai-director/status", "GET", None),
                ("/api/ai-director/story-outline", "POST", {"outline": "测试故事大纲"}),
                ("/api/ai-director/decision", "POST", {"event": {"type": "test", "data": "test"}}),
            ]
            
            base_url = "http://localhost:4001"
            
            for endpoint, method, data in api_endpoints:
                try:
                    url = base_url + endpoint
                    
                    if method == "GET":
                        response = await page.request.get(url)
                    else:
                        response = await page.request.post(url, data=json.dumps(data) if data else None)
                    
                    status = response.status
                    
                    if 200 <= status < 300:
                        print(f"✅ {method} {endpoint}: {status}")
                        api_results.append((endpoint, True))
                    else:
                        print(f"⚠️ {method} {endpoint}: {status}")
                        api_results.append((endpoint, False))
                        
                except Exception as e:
                    print(f"❌ {method} {endpoint}: {str(e)[:50]}")
                    api_results.append((endpoint, False))
            
        finally:
            await browser.close()
        
        # API测试总结
        if api_results:
            passed = sum(1 for _, result in api_results if result)
            print(f"\nAPI测试: {passed}/{len(api_results)} 端点可用")


async def main():
    """运行所有Playwright测试"""
    print("🎭 AI导演系统Playwright测试")
    print(f"   时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行Web界面测试
    web_test_passed = await test_ai_director_web_interface()
    
    # 运行API测试
    await test_ai_director_api()
    
    if web_test_passed:
        print("\n✅ Web界面测试通过!")
    else:
        print("\n⚠️ Web界面测试有失败项")


if __name__ == "__main__":
    asyncio.run(main())