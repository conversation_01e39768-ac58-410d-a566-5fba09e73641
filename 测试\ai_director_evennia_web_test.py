#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统Evennia正式Web页面测试
使用Playwright测试AI导演功能在真实Evennia环境中的表现
"""

import asyncio
import json
import time
from playwright.async_api import async_playwright, expect

class EvenniaWebTester:
    """Evennia Web界面测试器"""
    
    def __init__(self):
        self.base_url = "http://localhost:4001"
        self.test_results = []
        self.browser = None
        self.page = None
    
    async def setup(self):
        """初始化浏览器"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        
    async def teardown(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
    
    def log_result(self, test_name, success, message=""):
        """记录测试结果"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": time.strftime("%H:%M:%S")
        }
        self.test_results.append(result)
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
    
    async def test_evennia_homepage(self):
        """测试Evennia主页访问"""
        try:
            await self.page.goto(self.base_url)
            await self.page.wait_for_load_state('networkidle')
            
            # 检查页面标题
            title = await self.page.title()
            if "Evennia" in title or "testgame" in title:
                self.log_result("主页访问", True, f"页面标题: {title}")
                return True
            else:
                self.log_result("主页访问", False, f"意外的页面标题: {title}")
                return False
                
        except Exception as e:
            self.log_result("主页访问", False, f"访问失败: {str(e)}")
            return False
    
    async def test_webclient_access(self):
        """测试Web客户端访问"""
        try:
            webclient_url = f"{self.base_url}/webclient/"
            await self.page.goto(webclient_url)
            await self.page.wait_for_load_state('networkidle')
            
            # 检查是否有游戏界面元素
            game_elements = [
                'input[type="text"]',  # 命令输入框
                '.msg',  # 消息区域
                '#inputfield',  # 输入字段
                '.webclient'  # webclient容器
            ]
            
            found_elements = []
            for selector in game_elements:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=3000)
                    if element:
                        found_elements.append(selector)
                except:
                    pass
            
            if found_elements:
                self.log_result("Web客户端访问", True, f"找到游戏元素: {found_elements}")
                return True
            else:
                self.log_result("Web客户端访问", False, "未找到游戏界面元素")
                return False
                
        except Exception as e:
            self.log_result("Web客户端访问", False, f"访问失败: {str(e)}")
            return False
    
    async def test_login_process(self):
        """测试登录流程"""
        try:
            # 尝试找到登录表单
            login_selectors = [
                'input[name="username"]',
                'input[name="user"]',
                'input[type="text"]',
                '#username',
                '.username'
            ]
            
            username_input = None
            for selector in login_selectors:
                try:
                    username_input = await self.page.wait_for_selector(selector, timeout=2000)
                    if username_input:
                        break
                except:
                    continue
            
            if username_input:
                # 尝试登录
                await username_input.fill("admin")
                
                # 查找密码输入框
                password_selectors = [
                    'input[name="password"]',
                    'input[type="password"]',
                    '#password'
                ]
                
                password_input = None
                for selector in password_selectors:
                    try:
                        password_input = await self.page.query_selector(selector)
                        if password_input:
                            break
                    except:
                        continue
                
                if password_input:
                    await password_input.fill("TestPass123!")
                    
                    # 查找提交按钮
                    submit_selectors = [
                        'button[type="submit"]',
                        'input[type="submit"]',
                        'button:has-text("Login")',
                        'button:has-text("Connect")'
                    ]
                    
                    for selector in submit_selectors:
                        try:
                            submit_btn = await self.page.query_selector(selector)
                            if submit_btn:
                                await submit_btn.click()
                                break
                        except:
                            continue
                    
                    await self.page.wait_for_timeout(2000)
                    self.log_result("登录流程", True, "成功执行登录操作")
                    return True
                else:
                    self.log_result("登录流程", True, "找到用户名输入框但无密码框")
                    return True
            else:
                self.log_result("登录流程", False, "未找到登录表单")
                return False
                
        except Exception as e:
            self.log_result("登录流程", False, f"登录失败: {str(e)}")
            return False
    
    async def test_ai_director_api(self):
        """测试AI导演API端点"""
        try:
            # 测试故事大纲解析API
            api_url = f"{self.base_url}/api/ai-director/parse-outline/"
            
            # 使用页面的evaluate方法来发送API请求
            response = await self.page.evaluate("""
                async (url) => {
                    try {
                        const response = await fetch(url, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                outline_text: '《逆天改命》主题：凡人逆天修仙，挑战命运束缚'
                            })
                        });
                        const data = await response.json();
                        return { success: true, data: data, status: response.status };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                }
            """, api_url)
            
            if response.get('success'):
                if response.get('status') == 200:
                    self.log_result("AI导演API", True, "API响应正常")
                    return True
                else:
                    self.log_result("AI导演API", False, f"API返回状态码: {response.get('status')}")
                    return False
            else:
                self.log_result("AI导演API", False, f"API请求失败: {response.get('error')}")
                return False
                
        except Exception as e:
            self.log_result("AI导演API", False, f"API测试异常: {str(e)}")
            return False
    
    async def test_game_commands(self):
        """测试游戏命令输入"""
        try:
            # 查找命令输入框
            input_selectors = [
                'input[type="text"]',
                '#inputfield',
                '.input',
                'input.command'
            ]
            
            command_input = None
            for selector in input_selectors:
                try:
                    command_input = await self.page.query_selector(selector)
                    if command_input:
                        break
                except:
                    continue
            
            if command_input:
                # 测试基本命令
                test_commands = ["look", "help", "who"]
                
                for cmd in test_commands:
                    await command_input.fill(cmd)
                    await self.page.keyboard.press('Enter')
                    await self.page.wait_for_timeout(1000)
                
                self.log_result("游戏命令", True, f"成功测试命令: {test_commands}")
                return True
            else:
                self.log_result("游戏命令", False, "未找到命令输入框")
                return False
                
        except Exception as e:
            self.log_result("游戏命令", False, f"命令测试失败: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🎭 开始AI导演系统Evennia Web测试")
        print(f"   测试目标: {self.base_url}")
        print(f"   开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        await self.setup()
        
        try:
            # 执行测试序列
            tests = [
                ("Evennia主页访问", self.test_evennia_homepage),
                ("Web客户端访问", self.test_webclient_access),
                ("登录流程测试", self.test_login_process),
                ("AI导演API测试", self.test_ai_director_api),
                ("游戏命令测试", self.test_game_commands)
            ]
            
            for test_name, test_func in tests:
                print(f"\n🧪 执行测试: {test_name}")
                await test_func()
                await self.page.wait_for_timeout(1000)  # 测试间隔
            
        finally:
            await self.teardown()
        
        # 输出测试总结
        self.print_summary()
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        
        for result in self.test_results:
            status = "✅ 通过" if result['success'] else "❌ 失败"
            print(f"{result['test']}: {status}")
            if result['message']:
                print(f"   详情: {result['message']}")
        
        print(f"\n总计: {passed}/{total} 测试通过")
        
        if passed == total:
            print("\n🎉 所有测试通过!")
            print("AI导演系统在Evennia Web环境中工作正常")
        else:
            print(f"\n⚠️ 有 {total - passed} 个测试失败")
            print("需要检查AI导演系统的集成问题")


async def main():
    """主函数"""
    tester = EvenniaWebTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
