#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to create PostgreSQL database for xiuxian_mud
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def create_database():
    try:
        # Connect to PostgreSQL server (to the default 'postgres' database)
        connection = psycopg2.connect(
            user="postgres",
            password="zy123good",
            host="localhost",
            port="5432",
            database="postgres"  # Connect to default database first
        )
        
        connection.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = connection.cursor()
        
        # Check if database already exists
        cursor.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = 'xiuxian_mud'")
        exists = cursor.fetchone()
        
        if exists:
            print("Database 'xiuxian_mud' already exists!")
        else:
            # Create the database
            cursor.execute("CREATE DATABASE xiuxian_mud")
            print("Database 'xiuxian_mud' created successfully!")
            
    except psycopg2.Error as error:
        print(f"Error creating database: {error}")
    finally:
        if connection:
            cursor.close()
            connection.close()

if __name__ == "__main__":
    create_database() 