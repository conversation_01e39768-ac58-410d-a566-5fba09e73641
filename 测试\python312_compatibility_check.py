#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Python 3.12兼容性检查和修复
检查AI导演系统代码的Python 3.12兼容性
"""

import sys
import os
import ast
import importlib.util

class Python312CompatibilityChecker:
    """Python 3.12兼容性检查器"""
    
    def __init__(self):
        self.issues = []
        self.files_checked = 0
        
    def check_python_version(self):
        """检查Python版本"""
        version = sys.version_info
        print(f"🐍 当前Python版本: {version.major}.{version.minor}.{version.micro}")
        
        if version.major == 3 and version.minor == 12:
            print("✅ Python 3.12 - 完全兼容")
            return True
        elif version.major == 3 and version.minor >= 10:
            print("✅ Python 3.10+ - 兼容")
            return True
        else:
            print("⚠️ 建议使用Python 3.10-3.12")
            return False
    
    def check_file_syntax(self, filepath):
        """检查文件语法"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 尝试解析AST
            ast.parse(content)
            return True, None
        except SyntaxError as e:
            return False, str(e)
        except Exception as e:
            return False, str(e)
    
    def check_imports(self, filepath):
        """检查导入兼容性"""
        problematic_imports = []
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查已知的问题导入
            if 'telnetlib' in content:
                problematic_imports.append('telnetlib (removed in Python 3.13)')
            
            if 'imp' in content and 'import imp' in content:
                problematic_imports.append('imp (deprecated, use importlib)')
                
            return problematic_imports
        except Exception:
            return []
    
    def check_ai_director_files(self):
        """检查AI导演系统文件"""
        print("\n🔍 检查AI导演系统文件...")
        
        files_to_check = [
            'xiuxian_mud_new/systems/ai_director.py',
            'xiuxian_mud_new/systems/ai_client.py',
            'mygame/systems/handlers/ai_director_handler.py',
            'mygame/commands/ai_director_commands.py',
            'mygame/web/api/xiuxian_api.py'
        ]
        
        for filepath in files_to_check:
            if os.path.exists(filepath):
                print(f"\n📁 检查文件: {filepath}")
                self.files_checked += 1
                
                # 语法检查
                syntax_ok, syntax_error = self.check_file_syntax(filepath)
                if syntax_ok:
                    print("  ✅ 语法检查通过")
                else:
                    print(f"  ❌ 语法错误: {syntax_error}")
                    self.issues.append(f"{filepath}: 语法错误 - {syntax_error}")
                
                # 导入检查
                import_issues = self.check_imports(filepath)
                if not import_issues:
                    print("  ✅ 导入检查通过")
                else:
                    print("  ⚠️ 导入问题:")
                    for issue in import_issues:
                        print(f"    - {issue}")
                        self.issues.append(f"{filepath}: {issue}")
            else:
                print(f"⚠️ 文件不存在: {filepath}")
    
    def check_dependencies(self):
        """检查依赖兼容性"""
        print("\n📦 检查依赖兼容性...")
        
        dependencies = [
            ('evennia', '检查Evennia框架'),
            ('django', '检查Django框架'),
            ('requests', '检查HTTP请求库'),
            ('json', '检查JSON库'),
            ('socket', '检查Socket库'),
            ('threading', '检查线程库'),
            ('dataclasses', '检查数据类'),
            ('typing', '检查类型注解'),
            ('enum', '检查枚举类'),
            ('collections', '检查集合库')
        ]
        
        for module_name, description in dependencies:
            try:
                spec = importlib.util.find_spec(module_name)
                if spec is not None:
                    print(f"  ✅ {description}: 可用")
                else:
                    print(f"  ❌ {description}: 不可用")
                    self.issues.append(f"缺少依赖: {module_name}")
            except Exception as e:
                print(f"  ⚠️ {description}: 检查失败 - {e}")
    
    def generate_compatibility_fixes(self):
        """生成兼容性修复建议"""
        print("\n🔧 兼容性修复建议:")
        
        if not self.issues:
            print("✅ 未发现兼容性问题！")
            return
        
        print("发现以下问题:")
        for i, issue in enumerate(self.issues, 1):
            print(f"{i}. {issue}")
        
        print("\n💡 修复建议:")
        
        # telnetlib修复
        if any('telnetlib' in issue for issue in self.issues):
            print("📝 telnetlib修复:")
            print("   - 替换为socket库实现")
            print("   - 或使用第三方库如telnetlib3")
        
        # 其他修复建议
        print("📝 通用修复:")
        print("   - 确保使用Python 3.10-3.12")
        print("   - 更新所有依赖到最新兼容版本")
        print("   - 使用现代Python特性替代废弃功能")
    
    def create_fixed_telnet_client(self):
        """创建修复的telnet客户端"""
        print("\n🛠️ 创建Python 3.12兼容的telnet客户端...")
        
        fixed_client_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Python 3.12兼容的Telnet客户端
替代已移除的telnetlib模块
"""

import socket
import time
import threading

class CompatTelnetClient:
    """兼容的Telnet客户端"""
    
    def __init__(self, host, port, timeout=10):
        self.host = host
        self.port = port
        self.timeout = timeout
        self.sock = None
        self.connected = False
        
    def connect(self):
        """连接到服务器"""
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(self.timeout)
            self.sock.connect((self.host, self.port))
            self.connected = True
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def send(self, data):
        """发送数据"""
        if not self.connected:
            return False
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            self.sock.send(data)
            return True
        except Exception as e:
            print(f"发送失败: {e}")
            return False
    
    def receive(self, timeout=5):
        """接收数据"""
        if not self.connected:
            return b""
        try:
            self.sock.settimeout(timeout)
            data = self.sock.recv(4096)
            return data
        except socket.timeout:
            return b""
        except Exception as e:
            print(f"接收失败: {e}")
            return b""
    
    def close(self):
        """关闭连接"""
        if self.sock:
            try:
                self.sock.close()
            except:
                pass
        self.connected = False
'''
        
        with open('测试/compat_telnet_client.py', 'w', encoding='utf-8') as f:
            f.write(fixed_client_code)
        
        print("✅ 已创建: 测试/compat_telnet_client.py")
    
    def run_full_check(self):
        """运行完整检查"""
        print("🔍 Python 3.12兼容性检查启动...")
        print("="*60)
        
        # 检查Python版本
        version_ok = self.check_python_version()
        
        # 检查AI导演系统文件
        self.check_ai_director_files()
        
        # 检查依赖
        self.check_dependencies()
        
        # 生成修复建议
        self.generate_compatibility_fixes()
        
        # 创建修复的telnet客户端
        self.create_fixed_telnet_client()
        
        # 总结
        print("\n" + "="*60)
        print("📊 兼容性检查总结")
        print("="*60)
        print(f"检查文件数: {self.files_checked}")
        print(f"发现问题数: {len(self.issues)}")
        
        if not self.issues:
            print("🎉 所有检查通过！代码与Python 3.12完全兼容！")
        else:
            print("⚠️ 发现兼容性问题，请参考修复建议")
        
        print("\n💡 下一步:")
        print("1. 安装Python 3.12")
        print("2. 重新安装Evennia: pip install evennia")
        print("3. 应用修复建议")
        print("4. 重新测试AI导演系统")

def main():
    """主函数"""
    checker = Python312CompatibilityChecker()
    checker.run_full_check()

if __name__ == "__main__":
    main()
