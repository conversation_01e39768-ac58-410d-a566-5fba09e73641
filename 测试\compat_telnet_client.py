#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Python 3.12兼容的Telnet客户端
替代已移除的telnetlib模块
"""

import socket
import time
import threading

class CompatTelnetClient:
    """兼容的Telnet客户端"""
    
    def __init__(self, host, port, timeout=10):
        self.host = host
        self.port = port
        self.timeout = timeout
        self.sock = None
        self.connected = False
        
    def connect(self):
        """连接到服务器"""
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(self.timeout)
            self.sock.connect((self.host, self.port))
            self.connected = True
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def send(self, data):
        """发送数据"""
        if not self.connected:
            return False
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            self.sock.send(data)
            return True
        except Exception as e:
            print(f"发送失败: {e}")
            return False
    
    def receive(self, timeout=5):
        """接收数据"""
        if not self.connected:
            return b""
        try:
            self.sock.settimeout(timeout)
            data = self.sock.recv(4096)
            return data
        except socket.timeout:
            return b""
        except Exception as e:
            print(f"接收失败: {e}")
            return b""
    
    def close(self):
        """关闭连接"""
        if self.sock:
            try:
                self.sock.close()
            except:
                pass
        self.connected = False
