#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TagProperty高性能查询系统 - 性能基准测试脚本
"""
import os
import sys
import time
import random

# 设置Django环境
if not os.path.exists('server'):
    print("❌ 请在xiuxian_mud_new目录下运行此脚本")
    sys.exit(1)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
import django
django.setup()

from evennia.objects.models import ObjectDB
from evennia.accounts.models import AccountDB
from typeclasses.characters import Character

def cleanup_test_data():
    """清理测试数据"""
    print("\n🧹 清理旧的测试数据...")
    test_accounts = AccountDB.objects.filter(username__startswith="test_perf_")
    test_chars = ObjectDB.objects.filter(db_key__startswith="test_perf_")
    
    count_chars = test_chars.count()
    count_accounts = test_accounts.count()
    
    if count_chars > 0:
        test_chars.delete()
        print(f"   🗑️  删除了 {count_chars} 个测试角色。")
        
    if count_accounts > 0:
        test_accounts.delete()
        print(f"   🗑️  删除了 {count_accounts} 个测试账户。")
        
    if count_chars == 0 and count_accounts == 0:
        print("   ✅ 没有需要清理的数据。")

def setup_test_data(num_chars=1000):
    """创建测试数据"""
    print(f"\n🏗️  正在创建 {num_chars} 个测试角色...")
    
    realms = ["练气期", "筑基期", "金丹期", "元婴期"]
    alignments = ["正道", "魔道", "中立"]
    
    # 批量创建账户和角色
    accounts_to_create = []
    chars_to_create = []

    for i in range(num_chars):
        username = f"test_perf_{i}"
        char_name = f"test_perf_{i}"
        
        # 准备账户数据
        accounts_to_create.append(AccountDB(username=username))
        
        # 准备角色数据
        char_obj = Character(db_key=char_name)
        chars_to_create.append(char_obj)

    # 批量写入数据库
    AccountDB.objects.bulk_create(accounts_to_create, ignore_conflicts=True)
    Character.objects.bulk_create(chars_to_create, ignore_conflicts=True)
    
    # 批量添加Tags
    all_chars = Character.objects.filter(db_key__startswith="test_perf_")
    for char in all_chars:
        realm = random.choice(realms)
        alignment = random.choice(alignments)
        
        # 使用传统Attribute存储 (用于对比)
        char.db.realm_attr = realm
        char.db.alignment_attr = alignment
        
        # 使用TagProperty存储
        char.tags.add(realm, category="realm")
        char.tags.add(alignment, category="alignment")
    
    print(f"   ✅ 成功创建 {num_chars} 个角色并设置了属性和标签。")
    return num_chars

def run_benchmark():
    """执行基准测试"""
    print("\n⚡️ 开始执行性能基准测试...")
    print("="*60)
    
    # 1. 简单查询: 查找所有"金丹期"的角色
    print("\n📊 测试1: 简单查询 (查找所有'金丹期'的角色)")
    
    # 传统Attribute查询
    start_time = time.time()
    results_attr = ObjectDB.objects.filter(db_attributes__db_key="realm_attr", db_attributes__db_value="金丹期")
    count_attr = results_attr.count()
    end_time = time.time()
    duration_attr = (end_time - start_time) * 1000
    print(f"   - 传统Attribute查询: {duration_attr:.4f} ms (找到 {count_attr} 个)")
    
    # TagProperty查询
    start_time = time.time()
    results_tag = Character.objects.filter(db_tags__db_key="金丹期", db_tags__db_category="realm")
    count_tag = results_tag.count()
    end_time = time.time()
    duration_tag = (end_time - start_time) * 1000
    print(f"   - TagProperty查询:    {duration_tag:.4f} ms (找到 {count_tag} 个)")

    if duration_tag > 0:
        performance_gain = duration_attr / duration_tag
        print(f"   🚀 性能提升: {performance_gain:.2f} 倍")
        if duration_tag <= 5:
            print("   ✅ 验收标准达成: 查询响应时间 <= 5ms")
        else:
            print("   ❌ 验收标准未达成: 查询响应时间 > 5ms")
    else:
        print("   ⚠️ Tag查询过快，无法计算性能提升倍数。")

    # 2. 复合查询: 查找所有"元婴期"的"魔道"角色
    print("\n📊 测试2: 复合查询 (查找'元婴期'的'魔道'角色)")
    
    # 传统Attribute查询
    start_time = time.time()
    results_attr_compound = ObjectDB.objects.filter(
        db_attributes__db_key="realm_attr", db_attributes__db_value="元婴期"
    ).filter(
        db_attributes__db_key="alignment_attr", db_attributes__db_value="魔道"
    )
    count_attr_compound = results_attr_compound.count()
    end_time = time.time()
    duration_attr_compound = (end_time - start_time) * 1000
    print(f"   - 传统Attribute复合查询: {duration_attr_compound:.4f} ms (找到 {count_attr_compound} 个)")
    
    # TagProperty查询
    start_time = time.time()
    results_tag_compound = Character.objects.filter(
        db_tags__db_key="元婴期", db_tags__db_category="realm"
    ).filter(
        db_tags__db_key="魔道", db_tags__db_category="alignment"
    )
    count_tag_compound = results_tag_compound.count()
    end_time = time.time()
    duration_tag_compound = (end_time - start_time) * 1000
    print(f"   - TagProperty复合查询:    {duration_tag_compound:.4f} ms (找到 {count_tag_compound} 个)")
    
    if duration_tag_compound > 0:
        performance_gain_compound = duration_attr_compound / duration_tag_compound
        print(f"   🚀 性能提升: {performance_gain_compound:.2f} 倍")
        if performance_gain_compound >= 10:
             print("   ✅ 验收标准达成: 性能提升 >= 10倍")
        else:
            print("   ⚠️  性能提升 < 10倍")
    else:
        print("   ⚠️ Tag查询过快，无法计算性能提升倍数。")
        
    print("="*60)
    print("🎉 基准测试完成！")

if __name__ == "__main__":
    cleanup_test_data()
    setup_test_data(num_chars=1000)
    run_benchmark()
    cleanup_test_data() 