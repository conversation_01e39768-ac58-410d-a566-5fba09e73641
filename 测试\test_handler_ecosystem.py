#!/usr/bin/env python
"""
Handler生态组件化框架综合测试
验证内存优化70%+和事件通信机制
"""

import os
import sys
import time
import django
from django.conf import settings

# 设置Django环境
if not settings.configured:
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
    django.setup()

from systems.handler_system import (
    HandlerMemoryManager, HandlerCommunicationBus, HandlerDependencyManager,
    HandlerEventType, HandlerEvent, lazy_property, BaseHandler
)


class MockCharacter:
    """模拟Character对象"""
    def __init__(self, name="测试修仙者"):
        self.key = name
        self.id = f"test_char_{int(time.time() * 1000)}"
        self.name = name
        
    def msg(self, message):
        """模拟消息发送"""
        print(f"[{self.name}] {message}")


class TestHandler(BaseHandler):
    """测试Handler"""
    
    def initialize(self):
        super().initialize()
        self.test_data = {"initialized": True, "value": 0}
    
    def update_value(self, new_value):
        """更新测试值"""
        old_value = self.test_data.get("value", 0)
        self.test_data["value"] = new_value
        
        # 发布状态变化事件
        self.publish_event(
            HandlerEventType.HANDLER_STATE_CHANGED,
            data={"old_value": old_value, "new_value": new_value}
        )
    
    def get_value(self):
        """获取测试值"""
        return self.test_data.get("value", 0)


class TestCharacterWithHandlers:
    """带Handler的测试角色"""
    
    def __init__(self, name="测试角色"):
        self.key = name
        self.id = f"test_char_{int(time.time() * 1000)}"
        self.name = name
        self._handler_cache = {}
    
    @lazy_property
    def test_handler_1(self):
        """测试Handler 1"""
        return TestHandler(self)
    
    @lazy_property
    def test_handler_2(self):
        """测试Handler 2"""
        return TestHandler(self)
    
    @lazy_property
    def test_handler_3(self):
        """测试Handler 3"""
        return TestHandler(self)
    
    def msg(self, message):
        print(f"[{self.name}] {message}")


def test_memory_optimization():
    """测试内存优化功能"""
    print("\n=== 测试内存优化功能 ===")
    
    # 获取初始内存统计
    initial_report = HandlerMemoryManager.get_optimization_report()
    print(f"初始内存统计: {initial_report}")
    
    # 创建多个角色和Handler
    characters = []
    for i in range(10):
        char = TestCharacterWithHandlers(f"测试角色{i}")
        characters.append(char)
        
        # 访问Handler触发创建
        char.test_handler_1.update_value(i * 10)
        char.test_handler_2.update_value(i * 20)
        if i % 2 == 0:  # 只有一半角色使用第三个Handler
            char.test_handler_3.update_value(i * 30)
    
    # 获取创建后的内存统计
    after_creation_report = HandlerMemoryManager.get_optimization_report()
    print(f"创建后内存统计: {after_creation_report}")
    
    # 模拟一段时间后的清理
    print("模拟时间流逝...")
    time.sleep(1)
    
    # 手动触发清理
    cleaned_count = HandlerMemoryManager.cleanup_inactive_handlers(max_idle_time=0.5)
    print(f"清理了 {cleaned_count} 个Handler")
    
    # 获取清理后的内存统计
    after_cleanup_report = HandlerMemoryManager.get_optimization_report()
    print(f"清理后内存统计: {after_cleanup_report}")
    
    # 计算内存优化效果
    memory_saved = after_cleanup_report["memory_saved"]
    current_memory = after_cleanup_report["current_memory_usage"]
    if current_memory > 0:
        optimization_rate = (memory_saved / (memory_saved + current_memory)) * 100
        print(f"内存优化率: {optimization_rate:.1f}%")
        
        if optimization_rate >= 70:
            print("✓ 内存优化目标达成 (≥70%)")
            return True
        else:
            print(f"⚠️ 内存优化未达标 ({optimization_rate:.1f}% < 70%)")
            return False
    else:
        print("✓ 所有Handler已被清理，内存优化100%")
        return True


def test_event_communication():
    """测试事件通信机制"""
    print("\n=== 测试事件通信机制 ===")
    
    # 创建测试角色
    char1 = TestCharacterWithHandlers("角色1")
    char2 = TestCharacterWithHandlers("角色2")
    
    # 事件接收计数器
    event_received = {"count": 0, "events": []}
    
    def event_callback(event: HandlerEvent):
        event_received["count"] += 1
        event_received["events"].append(event)
        print(f"收到事件: {event.event_type.value} from {event.source_handler}")
    
    # 订阅事件
    HandlerCommunicationBus.subscribe(
        HandlerEventType.HANDLER_STATE_CHANGED, 
        "TestHandler", 
        event_callback
    )
    
    # 触发事件
    handler1 = char1.test_handler_1
    handler2 = char2.test_handler_1
    
    handler1.update_value(100)
    handler2.update_value(200)
    
    # 测试Handler间通信
    handler1.communicate_with_handler("TestHandler", "greeting", {"message": "Hello"})
    
    # 验证事件接收
    print(f"总共接收到 {event_received['count']} 个事件")
    
    if event_received["count"] >= 3:  # 2个状态变化 + 1个通信事件
        print("✓ 事件通信机制正常工作")
        return True
    else:
        print("✗ 事件通信机制异常")
        return False


def test_dependency_management():
    """测试依赖管理"""
    print("\n=== 测试依赖管理 ===")
    
    char = TestCharacterWithHandlers("依赖测试角色")
    
    # 获取Handler
    handler1 = char.test_handler_1
    handler2 = char.test_handler_2
    handler3 = char.test_handler_3
    
    # 添加依赖关系
    handler1.add_dependency("TestHandler")  # handler1 依赖 TestHandler
    
    # 检查依赖关系
    dependencies = HandlerDependencyManager.get_dependencies("TestHandler")
    dependents = HandlerDependencyManager.get_dependents("TestHandler")
    
    print(f"TestHandler的依赖: {dependencies}")
    print(f"依赖TestHandler的Handler: {dependents}")
    
    # 测试循环依赖检查
    has_circular = HandlerDependencyManager.check_circular_dependency("TestHandler", "TestHandler")
    print(f"循环依赖检查: {has_circular}")
    
    # 获取依赖图
    dependency_graph = HandlerDependencyManager.get_dependency_graph()
    print(f"依赖关系图: {dependency_graph}")
    
    if len(dependents) > 0:
        print("✓ 依赖管理系统正常工作")
        return True
    else:
        print("✗ 依赖管理系统异常")
        return False


def test_lazy_property_performance():
    """测试lazy_property性能"""
    print("\n=== 测试lazy_property性能 ===")
    
    char = TestCharacterWithHandlers("性能测试角色")
    
    # 测试首次访问时间
    start_time = time.time()
    handler1 = char.test_handler_1
    first_access_time = time.time() - start_time
    
    # 测试重复访问时间
    start_time = time.time()
    for _ in range(1000):
        _ = char.test_handler_1
    repeated_access_time = (time.time() - start_time) / 1000
    
    print(f"首次访问时间: {first_access_time:.6f}秒")
    print(f"重复访问平均时间: {repeated_access_time:.6f}秒")
    
    # 性能提升比率
    if repeated_access_time > 0:
        performance_ratio = first_access_time / repeated_access_time
        print(f"性能提升比率: {performance_ratio:.1f}x")
        
        if performance_ratio >= 10:  # 重复访问应该比首次访问快至少10倍
            print("✓ lazy_property性能优化有效")
            return True
        else:
            print("⚠️ lazy_property性能优化效果不明显")
            return False
    else:
        print("✓ 重复访问时间极短，性能优化极佳")
        return True


def main():
    """主测试函数"""
    print("Handler生态组件化框架综合测试开始")
    print("=" * 60)
    
    tests = [
        ("内存优化功能", test_memory_optimization),
        ("事件通信机制", test_event_communication),
        ("依赖管理系统", test_dependency_management),
        ("lazy_property性能", test_lazy_property_performance)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n开始测试: {test_name}")
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"Handler生态测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 Handler生态组件化框架测试全部通过！")
        print("✅ 内存优化70%+ - 完成")
        print("✅ 事件通信机制 - 完成")
        print("✅ 依赖管理系统 - 完成")
        print("✅ lazy_property性能优化 - 完成")
        print("\n🚀 Handler生态组件化框架开发完成！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步优化")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
