#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统Web API测试
测试AI导演功能的API端点在真实Evennia环境中的响应
"""

import requests
import json
import time
from urllib.parse import urljoin

class EvenniaAPITester:
    """Evennia API测试器"""
    
    def __init__(self, base_url="http://localhost:4001"):
        self.base_url = base_url
        self.test_results = []
        self.session = requests.Session()
        
    def log_result(self, test_name, success, message="", details=None):
        """记录测试结果"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "details": details,
            "timestamp": time.strftime("%H:%M:%S")
        }
        self.test_results.append(result)
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
        if details:
            print(f"   详情: {details}")
    
    def test_server_connectivity(self):
        """测试服务器连通性"""
        try:
            response = self.session.get(self.base_url, timeout=10)
            if response.status_code == 200:
                self.log_result("服务器连通性", True, f"状态码: {response.status_code}")
                return True
            else:
                self.log_result("服务器连通性", False, f"状态码: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_result("服务器连通性", False, f"连接失败: {str(e)}")
            return False
    
    def test_webclient_page(self):
        """测试Web客户端页面"""
        try:
            webclient_url = urljoin(self.base_url, "/webclient/")
            response = self.session.get(webclient_url, timeout=10)
            
            if response.status_code == 200:
                # 检查页面内容
                content = response.text.lower()
                game_indicators = ["evennia", "webclient", "input", "game", "mud"]
                found_indicators = [indicator for indicator in game_indicators if indicator in content]
                
                if found_indicators:
                    self.log_result("Web客户端页面", True, 
                                  f"页面正常，找到游戏元素: {found_indicators}")
                    return True
                else:
                    self.log_result("Web客户端页面", False, "页面内容不包含游戏元素")
                    return False
            else:
                self.log_result("Web客户端页面", False, f"状态码: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_result("Web客户端页面", False, f"请求失败: {str(e)}")
            return False
    
    def test_ai_director_parse_outline_api(self):
        """测试AI导演故事大纲解析API"""
        try:
            api_url = urljoin(self.base_url, "/api/ai-director/parse-outline/")
            
            test_data = {
                "outline_text": "《逆天改命》主题：凡人逆天修仙，挑战命运束缚。核心冲突：废灵根与天道的对抗。主要角色：林逸风、苏清雪、魔君血煞。"
            }
            
            headers = {
                'Content-Type': 'application/json',
                'X-CSRFToken': 'test'  # 简单的CSRF令牌
            }
            
            response = self.session.post(api_url, 
                                       json=test_data, 
                                       headers=headers, 
                                       timeout=30)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success'):
                        self.log_result("AI导演故事解析API", True, 
                                      f"解析成功: {result.get('title', '未知标题')}")
                        return True
                    else:
                        self.log_result("AI导演故事解析API", False, 
                                      f"API返回失败: {result.get('error', '未知错误')}")
                        return False
                except json.JSONDecodeError:
                    self.log_result("AI导演故事解析API", False, "响应不是有效的JSON")
                    return False
            elif response.status_code == 404:
                self.log_result("AI导演故事解析API", False, "API端点不存在 (404)")
                return False
            elif response.status_code == 403:
                self.log_result("AI导演故事解析API", False, "权限被拒绝 (403)")
                return False
            else:
                self.log_result("AI导演故事解析API", False, 
                              f"HTTP状态码: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_result("AI导演故事解析API", False, f"请求异常: {str(e)}")
            return False
    
    def test_ai_director_decision_api(self):
        """测试AI导演决策生成API"""
        try:
            api_url = urljoin(self.base_url, "/api/ai-director/make-decision/")
            
            test_data = {
                "event_type": "CultivationBreakthrough",
                "character": "testuser",
                "description": "玩家成功突破到筑基期",
                "location": "青云峰",
                "priority": "HIGH"
            }
            
            headers = {
                'Content-Type': 'application/json',
                'X-CSRFToken': 'test'
            }
            
            response = self.session.post(api_url, 
                                       json=test_data, 
                                       headers=headers, 
                                       timeout=30)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success'):
                        decision_type = result.get('decision_type', '未知')
                        response_time = result.get('response_time', 0)
                        self.log_result("AI导演决策API", True, 
                                      f"决策生成成功: {decision_type}, 响应时间: {response_time}ms")
                        return True
                    else:
                        self.log_result("AI导演决策API", False, 
                                      f"API返回失败: {result.get('error', '未知错误')}")
                        return False
                except json.JSONDecodeError:
                    self.log_result("AI导演决策API", False, "响应不是有效的JSON")
                    return False
            elif response.status_code == 404:
                self.log_result("AI导演决策API", False, "API端点不存在 (404)")
                return False
            else:
                self.log_result("AI导演决策API", False, 
                              f"HTTP状态码: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_result("AI导演决策API", False, f"请求异常: {str(e)}")
            return False
    
    def test_ai_director_status_api(self):
        """测试AI导演状态API"""
        try:
            api_url = urljoin(self.base_url, "/api/ai-director/status/")
            
            response = self.session.get(api_url, timeout=10)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success'):
                        status = result.get('status', '未知')
                        self.log_result("AI导演状态API", True, f"状态查询成功: {status}")
                        return True
                    else:
                        self.log_result("AI导演状态API", False, 
                                      f"状态查询失败: {result.get('error', '未知错误')}")
                        return False
                except json.JSONDecodeError:
                    self.log_result("AI导演状态API", False, "响应不是有效的JSON")
                    return False
            elif response.status_code == 404:
                self.log_result("AI导演状态API", False, "API端点不存在 (404)")
                return False
            else:
                self.log_result("AI导演状态API", False, 
                              f"HTTP状态码: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_result("AI导演状态API", False, f"请求异常: {str(e)}")
            return False
    
    def test_performance_benchmark(self):
        """测试性能基准"""
        try:
            api_url = urljoin(self.base_url, "/api/ai-director/make-decision/")
            
            test_data = {
                "event_type": "PerformanceTest",
                "character": "benchmark",
                "description": "性能测试事件"
            }
            
            headers = {
                'Content-Type': 'application/json',
                'X-CSRFToken': 'test'
            }
            
            response_times = []
            success_count = 0
            
            print("   执行5次性能测试...")
            for i in range(5):
                start_time = time.time()
                try:
                    response = self.session.post(api_url, 
                                               json={**test_data, "test_id": i}, 
                                               headers=headers, 
                                               timeout=10)
                    end_time = time.time()
                    response_time = (end_time - start_time) * 1000  # 转换为毫秒
                    
                    if response.status_code == 200:
                        success_count += 1
                        response_times.append(response_time)
                        print(f"     测试 {i+1}: {response_time:.1f}ms")
                    else:
                        print(f"     测试 {i+1}: 失败 (状态码: {response.status_code})")
                        
                except requests.exceptions.RequestException as e:
                    print(f"     测试 {i+1}: 异常 ({str(e)})")
                
                time.sleep(0.5)  # 测试间隔
            
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                max_time = max(response_times)
                min_time = min(response_times)
                
                performance_ok = avg_time < 1000  # 1秒内算正常
                
                self.log_result("性能基准测试", performance_ok, 
                              f"成功: {success_count}/5, 平均: {avg_time:.1f}ms, 范围: {min_time:.1f}-{max_time:.1f}ms")
                return performance_ok
            else:
                self.log_result("性能基准测试", False, "所有请求都失败了")
                return False
                
        except Exception as e:
            self.log_result("性能基准测试", False, f"测试异常: {str(e)}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🎭 开始AI导演系统Web API测试")
        print(f"   测试目标: {self.base_url}")
        print(f"   开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 执行测试序列
        tests = [
            ("服务器连通性", self.test_server_connectivity),
            ("Web客户端页面", self.test_webclient_page),
            ("AI导演故事解析API", self.test_ai_director_parse_outline_api),
            ("AI导演决策API", self.test_ai_director_decision_api),
            ("AI导演状态API", self.test_ai_director_status_api),
            ("性能基准测试", self.test_performance_benchmark)
        ]
        
        for test_name, test_func in tests:
            print(f"\n🧪 执行测试: {test_name}")
            test_func()
            time.sleep(1)  # 测试间隔
        
        # 输出测试总结
        self.print_summary()
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        
        for result in self.test_results:
            status = "✅ 通过" if result['success'] else "❌ 失败"
            print(f"{result['test']}: {status}")
            if result['message']:
                print(f"   {result['message']}")
        
        print(f"\n总计: {passed}/{total} 测试通过")
        
        if passed == total:
            print("\n🎉 所有测试通过!")
            print("AI导演系统API在Evennia环境中工作正常")
        else:
            print(f"\n⚠️ 有 {total - passed} 个测试失败")
            print("需要检查AI导演系统的API集成")


def main():
    """主函数"""
    tester = EvenniaAPITester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
