<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>AI导演系统演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeIn 1s ease-in;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .panel {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            animation: slideUp 0.8s ease-out;
        }
        
        .panel h2 {
            margin-top: 0;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 10px;
        }
        
        .story-input {
            width: 100%;
            min-height: 150px;
            padding: 10px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            background: rgba(255,255,255,0.05);
            color: #fff;
            font-size: 14px;
            resize: vertical;
        }
        
        .story-input::placeholder {
            color: rgba(255,255,255,0.6);
        }
        
        .button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a6f);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(238,90,111,0.4);
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(238,90,111,0.6);
        }
        
        .button:active {
            transform: translateY(0);
        }
        
        .event-trigger {
            background: rgba(255,255,255,0.05);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .event-trigger:hover {
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        
        .event-trigger h4 {
            margin: 0 0 5px 0;
            color: #ffd700;
        }
        
        .response-display {
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            min-height: 200px;
            animation: fadeIn 0.5s ease-in;
        }
        
        .response-item {
            margin: 10px 0;
            padding: 15px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            border-left: 4px solid #ffd700;
        }
        
        .response-item .time {
            font-size: 12px;
            opacity: 0.7;
            margin-bottom: 5px;
        }
        
        .response-item .content {
            font-size: 16px;
            line-height: 1.5;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-card .value {
            font-size: 28px;
            font-weight: bold;
            color: #ffd700;
        }
        
        .stat-card .label {
            font-size: 14px;
            opacity: 0.8;
            margin-top: 5px;
        }
        
        .story-phase {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            position: relative;
        }
        
        .phase-item {
            flex: 1;
            text-align: center;
            padding: 10px;
            background: rgba(255,255,255,0.05);
            margin: 0 5px;
            border-radius: 8px;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .phase-item.active {
            background: rgba(255,215,0,0.3);
            transform: scale(1.05);
        }
        
        .phase-item.completed {
            background: rgba(0,255,0,0.2);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { 
                opacity: 0;
                transform: translateY(20px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 AI导演系统演示</h1>
            <p>智能剧情规划引擎 - 基于LLM的动态叙事系统</p>
        </div>
        
        <div class="main-content">
            <!-- 左侧面板：故事大纲 -->
            <div class="panel">
                <h2>📜 故事大纲设定</h2>
                <textarea class="story-input" id="storyOutline" placeholder="请输入故事大纲...

示例：
《逆天改命》
主题：凡人逆天修仙，挑战命运束缚
核心冲突：废灵根修士与天道的对抗
主要角色：林逸风、苏清雪、魔君血煞

剧情要点：
1. 序章：凡人觉醒
2. 起承：拜入宗门
3. 高潮：天道考验
4. 结局：逆天改命">《逆天改命》
主题：凡人逆天修仙，挑战命运束缚
核心冲突：废灵根的林逸风挑战天道宿命
主要角色：林逸风、苏清雪、魔君血煞、天机老人

剧情要点：
1. 序章：林逸风在凡人村庄遭遇妖兽袭击，意外觉醒特殊体质
2. 起承：拜入青云宗外门，受尽欺凌但坚持修炼
3. 转折：秘境中获得上古传承，实力突飞猛进
4. 高潮：各方势力争夺主角身上的秘密，大战爆发
5. 结局：突破天道束缚，开辟新的修炼之路</textarea>
                
                <div style="margin-top: 15px;">
                    <button class="button" onclick="parseStoryOutline()">
                        🔍 解析故事大纲
                    </button>
                    <button class="button" onclick="loadSampleStory()">
                        📖 加载示例故事
                    </button>
                </div>
                
                <!-- 故事阶段显示 -->
                <div class="story-phase" id="storyPhases">
                    <div class="phase-item" data-phase="序章">序章</div>
                    <div class="phase-item" data-phase="起承">起承</div>
                    <div class="phase-item active" data-phase="高潮">高潮</div>
                    <div class="phase-item" data-phase="转合">转合</div>
                    <div class="phase-item" data-phase="终章">终章</div>
                </div>
            </div>
            
            <!-- 右侧面板：事件触发 -->
            <div class="panel">
                <h2>🎮 游戏事件触发</h2>
                
                <div class="event-trigger" onclick="triggerEvent('cultivation_breakthrough')">
                    <h4>⚡ 修炼突破</h4>
                    <p>林逸风成功突破到筑基期，引起天地异象</p>
                </div>
                
                <div class="event-trigger" onclick="triggerEvent('combat_victory')">
                    <h4>⚔️ 战斗胜利</h4>
                    <p>击败强敌，展现惊人实力</p>
                </div>
                
                <div class="event-trigger" onclick="triggerEvent('treasure_discovery')">
                    <h4>💎 发现宝物</h4>
                    <p>在秘境中发现上古遗宝</p>
                </div>
                
                <div class="event-trigger" onclick="triggerEvent('sect_conflict')">
                    <h4>🏛️ 宗门冲突</h4>
                    <p>卷入宗门之间的纷争</p>
                </div>
                
                <div class="event-trigger" onclick="triggerEvent('celestial_anomaly')">
                    <h4>🌟 天象异变</h4>
                    <p>天道显现异象，预示大事将发生</p>
                </div>
            </div>
        </div>
        
        <!-- AI响应显示区 -->
        <div class="panel">
            <h2>🎭 AI导演响应</h2>
            <div class="response-display" id="aiResponses">
                <div class="response-item">
                    <div class="time">系统就绪</div>
                    <div class="content">AI导演系统已启动，等待剧情事件...</div>
                </div>
            </div>
        </div>
        
        <!-- 性能统计 -->
        <div class="panel">
            <h2>📊 性能统计</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="value" id="totalDecisions">0</div>
                    <div class="label">总决策数</div>
                </div>
                <div class="stat-card">
                    <div class="value" id="avgResponseTime">0ms</div>
                    <div class="label">平均响应时间</div>
                </div>
                <div class="stat-card">
                    <div class="value" id="cacheHitRate">0%</div>
                    <div class="label">缓存命中率</div>
                </div>
                <div class="stat-card">
                    <div class="value" id="activeStorylines">1</div>
                    <div class="label">活跃剧情线</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟AI导演系统
        let aiDirector = {
            storyOutline: null,
            currentPhase: '高潮',
            stats: {
                totalDecisions: 0,
                totalResponseTime: 0,
                cacheHits: 0,
                cacheMisses: 0
            },
            decisionCache: {}
        };
        
        // AI响应模板
        const aiResponseTemplates = {
            cultivation_breakthrough: [
                "天地灵气剧烈震荡，金色雷云在青云峰上空凝聚。林逸风的突破引起了整个宗门的注意，有人惊叹，有人嫉妒，暗流涌动。",
                "筑基成功的瞬间，一道神秘的意念扫过。'有意思的小家伙...'虚空中似乎传来一声轻笑，预示着更大的风波即将来临。",
                "突破的异象惊动了隐世的强者。远在千里之外的魔君血煞睁开了双眼：'那个预言中的人，终于出现了吗？'"
            ],
            combat_victory: [
                "这一战震惊了所有人。废灵根能有如此战力？暗中观察的长老们开始重新评估这个弟子的价值。",
                "战斗余波未平，一道传音符破空而来：'小友好身手，老夫有一桩机缘，不知可有兴趣？'",
                "对手倒下的瞬间，林逸风体内的神秘力量微微波动。这股力量的来源，恐怕连他自己都不清楚。"
            ],
            treasure_discovery: [
                "上古遗宝认主的瞬间，整个秘境都在颤抖。这不是普通的法宝，而是能改变整个修真界格局的存在。",
                "宝物入手，却也招来了觊觎。暗中已有数道强大的神识锁定了这里，一场腥风血雨即将到来。",
                "古老的器灵苏醒：'终于等到你了，命定之人。但你可知道，得到我意味着什么吗？'"
            ],
            sect_conflict: [
                "宗门大比在即，各方势力暗中角力。林逸风不知不觉间，已经成为了这场博弈中的关键棋子。",
                "青云宗与魔道的冲突一触即发。在这个关键时刻，林逸风的选择将影响整个局势的走向。",
                "内门长老会议上，关于林逸风的争论愈发激烈。有人要除之而后快，有人却看到了希望。"
            ],
            celestial_anomaly: [
                "九星连珠，天道震动。这是大劫将至的征兆，也是机缘降临的预示。修真界将迎来千年未有之大变局。",
                "虚空裂缝中透出诡异的气息，仿佛有什么恐怖的存在正在苏醒。各大宗门纷纷召回弟子，准备应对。",
                "天机紊乱，命运长河出现分支。有大能者推算天机，却吐血而退：'变数已现，天道不可测。'"
            ]
        };
        
        function parseStoryOutline() {
            const outline = document.getElementById('storyOutline').value;
            if (!outline.trim()) {
                alert('请先输入故事大纲');
                return;
            }
            
            // 模拟解析过程
            addAIResponse('正在解析故事大纲...', true);
            
            setTimeout(() => {
                aiDirector.storyOutline = {
                    title: '逆天改命',
                    theme: '凡人逆天修仙',
                    mainConflict: '废灵根与天道的对抗',
                    keyCharacters: ['林逸风', '苏清雪', '魔君血煞'],
                    phases: ['序章', '起承', '高潮', '转合', '终章']
                };
                
                addAIResponse(`✅ 故事大纲解析完成！
                
📖 标题：${aiDirector.storyOutline.title}
🎯 主题：${aiDirector.storyOutline.theme}
⚔️ 核心冲突：${aiDirector.storyOutline.mainConflict}
👥 关键角色：${aiDirector.storyOutline.keyCharacters.join('、')}
📊 故事阶段：${aiDirector.storyOutline.phases.join(' → ')}`);
                
                updateStats();
            }, 1500);
        }
        
        function loadSampleStory() {
            const sampleStory = `《仙道长生》
主题：追求长生之道，探索修仙真谛
核心冲突：个人追求与天地大道的矛盾
主要角色：云逸尘、月清霜、道玄真人、妖王赤炎

背景设定：
- 九州大陆，灵气复苏的时代
- 正魔两道对立，妖族蠢蠢欲动
- 上古秘境频现，机缘与危机并存

剧情主线：
1. 云逸尘偶得仙缘，踏上修仙之路
2. 结识月清霜，共历生死考验
3. 发现上古阴谋，涉及三界存亡
4. 最终悟道，开创新的修炼体系`;
            
            document.getElementById('storyOutline').value = sampleStory;
            addAIResponse('已加载示例故事《仙道长生》');
        }
        
        function triggerEvent(eventType) {
            // 生成缓存键
            const cacheKey = `${eventType}_${aiDirector.currentPhase}`;
            
            // 记录开始时间
            const startTime = performance.now();
            
            // 检查缓存
            if (aiDirector.decisionCache[cacheKey]) {
                aiDirector.stats.cacheHits++;
                const response = aiDirector.decisionCache[cacheKey];
                const responseTime = performance.now() - startTime;
                
                addAIResponse(`[缓存命中] ${response}`, false, responseTime);
                updateStats(responseTime);
                return;
            }
            
            aiDirector.stats.cacheMisses++;
            
            // 模拟AI决策过程
            addAIResponse('AI导演正在分析事件影响...', true);
            
            // 模拟网络延迟
            const delay = Math.random() * 100 + 50; // 50-150ms
            
            setTimeout(() => {
                const responses = aiResponseTemplates[eventType];
                const response = responses[Math.floor(Math.random() * responses.length)];
                const responseTime = performance.now() - startTime;
                
                // 加入缓存
                aiDirector.decisionCache[cacheKey] = response;
                
                // 根据事件类型生成决策
                let decisionType = '剧情推进';
                switch(eventType) {
                    case 'cultivation_breakthrough':
                        decisionType = '角色发展';
                        break;
                    case 'sect_conflict':
                        decisionType = '冲突解决';
                        break;
                    case 'celestial_anomaly':
                        decisionType = '世界事件';
                        break;
                    case 'treasure_discovery':
                        decisionType = '机缘降临';
                        break;
                }
                
                addAIResponse(`[${decisionType}] ${response}`, false, responseTime);
                updateStats(responseTime);
                
                // 可能推进剧情阶段
                if (Math.random() > 0.7) {
                    advancePhase();
                }
            }, delay);
        }
        
        function addAIResponse(content, isLoading = false, responseTime = null) {
            const responseDiv = document.getElementById('aiResponses');
            const responseItem = document.createElement('div');
            responseItem.className = 'response-item';
            responseItem.style.animation = 'slideUp 0.5s ease-out';
            
            const time = new Date().toLocaleTimeString();
            const timeStr = responseTime ? `${time} (响应时间: ${responseTime.toFixed(1)}ms)` : time;
            
            responseItem.innerHTML = `
                <div class="time">${timeStr}</div>
                <div class="content">
                    ${isLoading ? '<span class="loading"></span> ' : ''}
                    ${content}
                </div>
            `;
            
            responseDiv.insertBefore(responseItem, responseDiv.firstChild);
            
            // 保持最多10条记录
            while (responseDiv.children.length > 10) {
                responseDiv.removeChild(responseDiv.lastChild);
            }
        }
        
        function updateStats(responseTime = null) {
            if (responseTime) {
                aiDirector.stats.totalDecisions++;
                aiDirector.stats.totalResponseTime += responseTime;
            }
            
            const avgTime = aiDirector.stats.totalDecisions > 0 
                ? (aiDirector.stats.totalResponseTime / aiDirector.stats.totalDecisions).toFixed(1)
                : 0;
            
            const cacheTotal = aiDirector.stats.cacheHits + aiDirector.stats.cacheMisses;
            const hitRate = cacheTotal > 0 
                ? ((aiDirector.stats.cacheHits / cacheTotal) * 100).toFixed(1)
                : 0;
            
            document.getElementById('totalDecisions').textContent = aiDirector.stats.totalDecisions;
            document.getElementById('avgResponseTime').textContent = `${avgTime}ms`;
            document.getElementById('cacheHitRate').textContent = `${hitRate}%`;
            document.getElementById('activeStorylines').textContent = 
                Object.keys(aiDirector.decisionCache).length;
        }
        
        function advancePhase() {
            const phases = ['序章', '起承', '高潮', '转合', '终章'];
            const currentIndex = phases.indexOf(aiDirector.currentPhase);
            
            if (currentIndex < phases.length - 1) {
                aiDirector.currentPhase = phases[currentIndex + 1];
                
                // 更新UI
                document.querySelectorAll('.phase-item').forEach((item, index) => {
                    item.classList.remove('active');
                    if (index < currentIndex + 1) {
                        item.classList.add('completed');
                    }
                    if (index === currentIndex + 1) {
                        item.classList.add('active');
                    }
                });
                
                addAIResponse(`📖 剧情推进到新阶段：${aiDirector.currentPhase}`);
            }
        }
        
        // 页面加载完成后的初始化
        window.onload = function() {
            // 显示欢迎信息
            setTimeout(() => {
                addAIResponse('🎭 AI导演系统演示已启动！请先解析故事大纲，然后触发游戏事件查看AI响应。');
            }, 500);
        };
    </script>
</body>
</html>