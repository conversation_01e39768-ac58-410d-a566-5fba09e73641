#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统实际功能测试
在真实的mygame环境中测试AI导演功能
"""

import requests
import json
import time
import sys
import os

class AIDirectorLiveTest:
    """AI导演系统实际功能测试"""
    
    def __init__(self):
        self.base_url = "http://localhost:4001"  # 使用外部端口
        self.api_base = f"{self.base_url}/api/xiuxian"
        self.test_results = []
        
    def test_api_connection(self):
        """测试API连接"""
        print("🔗 测试API连接...")
        try:
            response = requests.get(f"{self.api_base}/cultivation/realm/", timeout=10)
            if response.status_code == 200:
                print("✅ API连接成功")
                return True
            else:
                print(f"❌ API连接失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API连接异常: {e}")
            return False
    
    def test_ai_director_story_status(self):
        """测试AI导演故事状态"""
        print("\n🎭 测试AI导演故事状态...")
        try:
            response = requests.get(f"{self.api_base}/ai-director/story-status/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 获取故事状态成功")
                print(f"   响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                self.test_results.append({
                    "test": "AI导演故事状态",
                    "status": "成功",
                    "data": data
                })
                return True
            else:
                print(f"❌ 获取故事状态失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取故事状态异常: {e}")
            return False
    
    def test_ai_director_world_state(self):
        """测试AI导演世界状态"""
        print("\n🌍 测试AI导演世界状态...")
        try:
            response = requests.get(f"{self.api_base}/ai-director/world-state/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 获取世界状态成功")
                print(f"   响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                self.test_results.append({
                    "test": "AI导演世界状态",
                    "status": "成功",
                    "data": data
                })
                return True
            else:
                print(f"❌ 获取世界状态失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取世界状态异常: {e}")
            return False
    
    def test_ai_director_update_context(self):
        """测试AI导演上下文更新"""
        print("\n📝 测试AI导演上下文更新...")
        try:
            test_context = "玩家开始修炼，天地灵气汇聚，似有异象将现"
            payload = {"context": test_context}
            
            response = requests.post(
                f"{self.api_base}/ai-director/update-context/",
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 更新上下文成功")
                print(f"   输入上下文: {test_context}")
                print(f"   响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                self.test_results.append({
                    "test": "AI导演上下文更新",
                    "status": "成功",
                    "input": test_context,
                    "data": data
                })
                return True
            else:
                print(f"❌ 更新上下文失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 更新上下文异常: {e}")
            return False
    
    def test_cultivation_integration(self):
        """测试修炼系统与AI导演的集成"""
        print("\n🧘‍♂️ 测试修炼系统与AI导演集成...")
        try:
            # 先获取当前境界
            response = requests.get(f"{self.api_base}/cultivation/realm/", timeout=10)
            if response.status_code == 200:
                realm_data = response.json()
                print(f"✅ 当前境界: {json.dumps(realm_data, ensure_ascii=False)}")
                
                # 开始修炼
                response = requests.post(f"{self.api_base}/cultivation/cultivate/", timeout=10)
                if response.status_code == 200:
                    cultivate_data = response.json()
                    print(f"✅ 开始修炼: {json.dumps(cultivate_data, ensure_ascii=False)}")
                    
                    # 等待一下，然后检查AI导演是否有响应
                    time.sleep(2)
                    
                    # 再次获取故事状态，看是否有变化
                    response = requests.get(f"{self.api_base}/ai-director/story-status/", timeout=10)
                    if response.status_code == 200:
                        story_data = response.json()
                        print(f"✅ 修炼后故事状态: {json.dumps(story_data, ensure_ascii=False, indent=2)}")
                        
                        self.test_results.append({
                            "test": "修炼系统与AI导演集成",
                            "status": "成功",
                            "realm": realm_data,
                            "cultivation": cultivate_data,
                            "story_after": story_data
                        })
                        return True
                    
            return False
        except Exception as e:
            print(f"❌ 修炼系统集成测试异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始AI导演系统实际功能测试...")
        print("="*60)
        
        # 测试API连接
        if not self.test_api_connection():
            print("❌ API连接失败，无法继续测试")
            return
        
        # 测试AI导演各项功能
        tests = [
            self.test_ai_director_story_status,
            self.test_ai_director_world_state,
            self.test_ai_director_update_context,
            self.test_cultivation_integration
        ]
        
        success_count = 0
        for test in tests:
            try:
                if test():
                    success_count += 1
                time.sleep(1)  # 测试间隔
            except Exception as e:
                print(f"❌ 测试执行异常: {e}")
        
        # 生成测试报告
        self.generate_report(success_count, len(tests))
    
    def generate_report(self, success_count, total_count):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 AI导演系统实际功能测试报告")
        print("="*60)
        
        print(f"总测试数: {total_count}")
        print(f"成功: {success_count}")
        print(f"失败: {total_count - success_count}")
        print(f"成功率: {(success_count/total_count*100):.1f}%")
        
        # 保存详细报告
        report_file = "ai_director_live_test_report.json"
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": time.time(),
                "summary": {
                    "total": total_count,
                    "success": success_count,
                    "failed": total_count - success_count,
                    "success_rate": (success_count/total_count*100)
                },
                "results": self.test_results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        if success_count == total_count:
            print("\n🎉 所有测试通过！AI导演系统在mygame中运行正常！")
        else:
            print(f"\n⚠️ 有 {total_count - success_count} 个测试失败，请检查相关功能。")

def main():
    """主函数"""
    print("🧙‍♂️ AI导演系统实际功能测试启动...")
    print("确保Evennia服务器正在运行在 http://localhost:4001")
    print("测试将在3秒后开始...")
    time.sleep(3)
    
    tester = AIDirectorLiveTest()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
