# -*- coding: utf-8 -*-
"""
通用指令模块
"""

from evennia import Command
from typeclasses.characters import Character
import time

class CmdFind(Command):
    """
    使用TagProperty系统查找修仙者

    用法:
      @find <境界>

    示例:
      @find 金丹期
      @find 元婴期

    利用高性能的TagProperty系统，快速查找特定境界的在线玩家。
    """
    key = "@find"
    aliases = ["@查找"]
    locks = "cmd:perm(Builders)"  # 仅限Builders及以上权限使用
    help_category = "通用"

    def parse(self):
        """解析参数"""
        self.args = self.args.strip()

    def func(self):
        """执行命令"""
        caller = self.caller
        if not self.args:
            caller.msg("用法: @find <境界>")
            return

        realm_to_find = self.args
        start_time = time.time()

        # 使用TagProperty进行查询
        found_chars = Character.objects.filter(
            db_tags__db_key=realm_to_find,
            db_tags__db_category="realm"
        )
        
        duration = (time.time() - start_time) * 1000
        
        if not found_chars:
            caller.msg(f"未找到任何处于 |y{realm_to_find}|n 的修仙者。")
            return
            
        table = self.caller.styled_table(
            "|w境界",
            "|w道号",
            "|w位置"
        )
        for char in found_chars:
            table.add_row(
                f"|y{realm_to_find}|n",
                char.key,
                char.location.key if char.location else "虚无"
            )
            
        caller.msg(f"|g在 {duration:.2f} 毫秒内找到了 {len(found_chars)} 位 |y{realm_to_find}|g 的修仙者:|n")
        caller.msg(table) 