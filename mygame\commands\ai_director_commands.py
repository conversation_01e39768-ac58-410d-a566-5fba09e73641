#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统命令
为超级管理员提供AI导演功能的游戏内命令
"""

from evennia import Command
from evennia.utils.evtable import EvTable
import json
import time

class CmdAIDirector(Command):
    """
    AI导演系统管理命令
    
    用法:
        aidirector status - 查看AI导演状态
        aidirector story <故事大纲> - 解析故事大纲
        aidirector event <事件描述> - 触发AI决策
        aidirector stats - 查看性能统计
    
    这个命令只有超级管理员可以使用。
    """
    
    key = "aidirector"
    aliases = ["aid", "导演"]
    locks = "cmd:perm(Developer)"
    help_category = "Admin"
    
    def func(self):
        """执行命令"""
        caller = self.caller
        args = self.args.strip()
        
        if not args:
            self.show_help()
            return
        
        # 解析参数
        parts = args.split(' ', 1)
        action = parts[0].lower()
        content = parts[1] if len(parts) > 1 else ""
        
        # 获取AI导演Handler
        try:
            from systems.handlers.ai_director_handler import AIDirectorHandler
            ai_handler = AIDirectorHandler(caller)
        except ImportError:
            caller.msg("|r❌ AI导演系统未正确安装|n")
            return
        
        # 执行相应操作
        if action == "status":
            self.show_status(ai_handler)
        elif action == "story":
            self.parse_story(ai_handler, content)
        elif action == "event":
            self.trigger_event(ai_handler, content)
        elif action == "stats":
            self.show_stats(ai_handler)
        else:
            self.show_help()
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
|c🎭 AI导演系统命令帮助|n

|y用法:|n
  |waidirector status|n     - 查看AI导演状态
  |waidirector story <大纲>|n - 解析故事大纲
  |waidirector event <事件>|n - 触发AI决策
  |waidirector stats|n      - 查看性能统计

|y示例:|n
  |waidirector status|n
  |waidirector story 《逆天改命》主角林逸风...|n
  |waidirector event 玩家开始修炼九转玄功|n
  |waidirector stats|n

|r注意: 此命令仅限超级管理员使用|n
        """
        self.caller.msg(help_text)
    
    def show_status(self, ai_handler):
        """显示AI导演状态"""
        try:
            story_status = ai_handler.get_story_status()
            world_state = ai_handler.get_world_state()
            
            table = EvTable("|c属性|n", "|c值|n", border="cells")
            table.add_row("|y系统状态|n", "|g运行正常|n")
            table.add_row("|y当前故事|n", story_status.get('current_story', '无'))
            table.add_row("|y故事阶段|n", story_status.get('current_phase', '未知'))
            table.add_row("|y活跃剧情线|n", str(len(story_status.get('active_threads', []))))
            table.add_row("|y世界事件|n", str(len(world_state.get('events', []))))
            
            self.caller.msg("|c🎭 AI导演系统状态|n")
            self.caller.msg(str(table))
            
        except Exception as e:
            self.caller.msg(f"|r❌ 获取状态失败: {e}|n")
    
    def parse_story(self, ai_handler, story_text):
        """解析故事大纲"""
        if not story_text:
            self.caller.msg("|r❌ 请提供故事大纲内容|n")
            return
        
        try:
            self.caller.msg("|c🤖 AI导演正在解析故事大纲...|n")
            
            # 模拟解析过程
            time.sleep(1)
            
            result = ai_handler.parse_story_outline(story_text)
            
            if result.get('success'):
                self.caller.msg("|g✅ 故事大纲解析成功！|n")
                self.caller.msg(f"|y故事标题:|n {result.get('title', '未知')}")
                self.caller.msg(f"|y故事主题:|n {result.get('theme', '未知')}")
                self.caller.msg(f"|y核心冲突:|n {result.get('main_conflict', '未知')}")
                self.caller.msg(f"|y关键角色:|n {', '.join(result.get('key_characters', []))}")
                self.caller.msg(f"|y剧情点数量:|n {result.get('plot_points', 0)}")
                self.caller.msg(f"|y故事阶段:|n {' → '.join(result.get('phases', []))}")
            else:
                self.caller.msg(f"|r❌ 解析失败: {result.get('error', '未知错误')}|n")
                
        except Exception as e:
            self.caller.msg(f"|r❌ 解析异常: {e}|n")
    
    def trigger_event(self, ai_handler, event_text):
        """触发AI事件决策"""
        if not event_text:
            self.caller.msg("|r❌ 请提供事件描述|n")
            return
        
        try:
            self.caller.msg("|c🧠 AI导演正在分析事件并生成决策...|n")
            
            # 构建事件数据
            event_data = {
                "event_type": "admin_trigger",
                "player": self.caller.name,
                "description": event_text,
                "timestamp": time.time(),
                "location": str(self.caller.location) if self.caller.location else "未知"
            }
            
            # 模拟AI决策过程
            time.sleep(1.5)
            
            # 更新上下文并获取决策
            result = ai_handler.update_context(event_text)
            
            if result.get('success'):
                self.caller.msg("|g✅ AI决策生成完成！|n")
                
                # 显示决策结果
                decision_content = result.get('ai_response', '天地灵气震荡，似有异象将现...')
                self.caller.msg(f"|c🎭 AI导演决策:|n")
                self.caller.msg(f"|w{decision_content}|n")
                
                # 如果有建议的后续行动
                if 'suggestions' in result:
                    self.caller.msg(f"|y💡 建议后续行动:|n")
                    for suggestion in result['suggestions']:
                        self.caller.msg(f"  • {suggestion}")
                
                # 广播给房间内的其他玩家
                if self.caller.location:
                    self.caller.location.msg_contents(
                        f"|c🎭 {decision_content}|n",
                        exclude=[self.caller]
                    )
                    
            else:
                self.caller.msg(f"|r❌ 决策生成失败: {result.get('error', '未知错误')}|n")
                
        except Exception as e:
            self.caller.msg(f"|r❌ 事件处理异常: {e}|n")
    
    def show_stats(self, ai_handler):
        """显示性能统计"""
        try:
            stats = ai_handler.get_ai_performance_stats()
            
            table = EvTable("|c指标|n", "|c数值|n", border="cells")
            table.add_row("|y总决策数|n", str(stats.get('total_decisions', 0)))
            table.add_row("|y平均响应时间|n", f"{stats.get('average_response_time', 0):.3f}秒")
            table.add_row("|y缓存命中率|n", f"{stats.get('cache_hit_rate', 0):.1f}%")
            table.add_row("|y处理故事数|n", str(stats.get('stories_processed', 0)))
            table.add_row("|y活跃线程数|n", str(stats.get('active_threads', 0)))
            
            self.caller.msg("|c📊 AI导演性能统计|n")
            self.caller.msg(str(table))
            
            # 显示系统评级
            hit_rate = stats.get('cache_hit_rate', 0)
            response_time = stats.get('average_response_time', 0)
            
            if hit_rate > 70 and response_time < 0.1:
                rating = "|gA级|n"
            elif hit_rate > 50 and response_time < 0.2:
                rating = "|yB级|n"
            else:
                rating = "|rC级|n"
            
            self.caller.msg(f"|c🚀 系统性能评级: {rating}|n")
            
        except Exception as e:
            self.caller.msg(f"|r❌ 获取统计失败: {e}|n")


class CmdStoryDemo(Command):
    """
    故事演示命令 - 展示AI导演功能
    
    用法:
        storydemo - 运行AI导演功能演示
    
    这个命令展示AI导演系统如何为仙侠MUD生成智能剧情。
    """
    
    key = "storydemo"
    aliases = ["故事演示"]
    locks = "cmd:perm(Developer)"
    help_category = "Admin"
    
    def func(self):
        """执行演示"""
        caller = self.caller
        
        caller.msg("|c🎭 AI导演系统功能演示|n")
        caller.msg("|y=" * 50 + "|n")
        
        # 演示1: 故事大纲解析
        caller.msg("|c📖 演示1: 故事大纲智能解析|n")
        story_outline = """《逆天改命》
主题：凡人逆天修仙
林逸风本是废灵根，却因神秘传承逆天改命..."""
        
        caller.msg(f"|w输入故事大纲:|n {story_outline}")
        caller.msg("|g✅ AI解析结果:|n")
        caller.msg("  |y标题:|n 逆天改命")
        caller.msg("  |y主题:|n 凡人逆天修仙")
        caller.msg("  |y冲突:|n 废灵根与天道对抗")
        caller.msg("  |y角色:|n 林逸风, 苏清雪, 魔尊血煞")
        
        # 演示2: AI决策生成
        caller.msg("\n|c🧠 演示2: AI智能决策生成|n")
        caller.msg("|w触发事件:|n 玩家开始修炼九转玄功")
        caller.msg("|g✅ AI决策:|n")
        caller.msg("|w天地灵气感应到修炼者的诚心，开始汇聚。远处传来若有若无的龙吟声，似乎有什么即将觉醒...|n")
        
        # 演示3: 实时剧情
        caller.msg("\n|c🎬 演示3: 实时剧情生成|n")
        caller.msg("|w当前场景:|n 青云峰洞府")
        caller.msg("|g🎭 AI剧情:|n")
        caller.msg("|w突然，洞府内灵气暴动！你感觉到体内有股神秘力量在觉醒...|n")
        
        # 广播给房间内的其他人
        if caller.location:
            caller.location.msg_contents(
                "|c🎭 天地灵气突然汇聚，似有异象将现...|n",
                exclude=[caller]
            )
        
        caller.msg("\n|g🎉 演示完成！AI导演系统已为您的仙侠世界提供智能剧情支持！|n")
