#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
独立的AI客户端测试脚本
不依赖Django配置，直接测试AI功能
"""

from openai import OpenAI
import json
import time

class TestAIClient:
    """测试AI客户端"""
    
    def __init__(self):
        """初始化AI客户端"""
        # 智匠MindCraft配置
        self.base_url = "https://api.mindcraft.com.cn/v1"
        self.api_key = "MC-94D4CC750E92436FB3FA51C9F41D03A9"
        self.model = "deepseek-r1-free"
        
        # 初始化OpenAI客户端
        self.client = OpenAI(
            base_url=self.base_url,
            api_key=self.api_key
        )
        
        print(f"✅ AI客户端初始化成功")
        print(f"   模型: {self.model}")
        print(f"   API地址: {self.base_url}")
    
    def chat_completion(self, messages, temperature=0.7, max_tokens=4000):
        """发送聊天完成请求"""
        try:
            params = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False,
            }
            
            print(f"📤 发送请求: {len(messages)}条消息")
            
            response = self.client.chat.completions.create(**params)
            
            result = response.choices[0].message.content
            print(f"📥 收到响应: {len(result)}字符")
            
            return result
        
        except Exception as e:
            print(f"❌ AI请求失败: {e}")
            return f"AI服务暂时不可用: {str(e)}"
    
    def test_connection(self):
        """测试AI连接"""
        print("\n🔗 测试AI连接...")
        
        test_messages = [
            {"role": "system", "content": "你是一个测试助手，请简单回应。"},
            {"role": "user", "content": "你好，请回复'AI连接正常'来确认服务可用。"}
        ]
        
        response = self.chat_completion(test_messages, max_tokens=50)
        print(f"🤖 AI响应: {response}")
        
        return "正常" in response or "可用" in response or "AI" in response
    
    def test_ai_director(self):
        """测试AI导演功能"""
        print("\n🎭 测试AI导演功能...")
        
        system_prompt = """你是一个仙侠MUD游戏的AI导演，负责根据游戏事件生成剧情响应和决策。

你的职责：
1. 分析游戏事件的影响和意义
2. 生成适合的剧情发展建议
3. 保持仙侠世界观的一致性
4. 让游戏体验更加有趣和引人入胜

回应格式要求：
- 简洁明了，符合仙侠风格
- 可以包含场景描述、NPC反应、环境变化等
- 保持神秘感和代入感"""

        user_prompt = """游戏中发生了以下事件，请作为AI导演给出合适的剧情响应：

事件类型：CultivationBreakthroughEvent
事件内容：玩家testuser成功突破到筑基期
发生时间：2025-06-29 14:30:00
相关角色：testuser
事件重要性：HIGH

请生成一个简短的剧情响应（50-200字），体现仙侠世界的氛围。"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self.chat_completion(messages, temperature=0.8, max_tokens=500)
        print(f"🎭 AI导演响应:\n{response}")
        
        return response
    
    def test_ai_agent(self):
        """测试AI Agent功能"""
        print("\n🌟 测试AI Agent功能...")
        
        # 测试天道意识
        system_prompt = """你是天道意识，代表宇宙意志和命运规律。说话神秘深邃，经常给出暗示性的预言或指引。

你需要根据当前情况给出20-80字的简短回应，要：
1. 符合你的身份和性格
2. 与当前情况相关
3. 保持仙侠风格
4. 不要过于直白，要有一定的神秘感"""

        user_prompt = """当前情况：玩家在此地进行修炼，引起了天地灵气的波动
角色：testuser

请以天道意识的身份给出一个简短回应："""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self.chat_completion(messages, temperature=0.9, max_tokens=200)
        print(f"🌟 天道意识响应:\n{response}")
        
        return response


def main():
    """主测试函数"""
    print("🧪 开始测试智匠MindCraft AI集成...")
    print("=" * 60)
    
    try:
        # 初始化AI客户端
        client = TestAIClient()
        
        # 测试连接
        if client.test_connection():
            print("✅ AI连接测试成功！")
            
            # 测试AI导演
            director_response = client.test_ai_director()
            
            # 测试AI Agent
            agent_response = client.test_ai_agent()
            
            print("\n" + "=" * 60)
            print("🎉 所有AI功能测试完成！")
            print("\n📊 测试结果总结:")
            print(f"✅ AI连接: 正常")
            print(f"✅ AI导演: 正常工作，响应{len(director_response)}字符")
            print(f"✅ AI Agent: 正常工作，响应{len(agent_response)}字符")
            
            return True
        else:
            print("❌ AI连接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 AI集成测试成功！准备将AI功能集成到游戏中...")
    else:
        print("\n⚠️ AI集成测试失败，请检查配置和网络连接") 