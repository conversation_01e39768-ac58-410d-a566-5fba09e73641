#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修仙MUD API视图 - 基于Evennia的Django REST Framework
提供Handler功能的HTTP接口用于Playwright测试
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
import json
import logging

# 导入修仙MUD Handler系统
try:
    from mygame.systems.handlers.cultivation_handler import CultivationHandler
    from mygame.systems.handlers.combat_skill_handler import CombatSkillHandler
    from mygame.systems.handlers.alchemy_handler import AlchemyHandler
    from mygame.systems.handlers.karma_handler import KarmaHandler
    from mygame.systems.handlers.ai_director_handler import AIDirectorHandler
except ImportError as e:
    logging.error(f"无法导入Handler系统: {e}")
    # 创建模拟Handler用于测试
    class MockHandler:
        def __init__(self, *args, **kwargs):
            pass
        def get_current_realm(self):
            return ("练气期", 1)
        def get_cultivation_progress(self):
            return {}
        def start_cultivation(self):
            return {"success": False, "message": "Handler未正确加载"}
    
    CultivationHandler = MockHandler
    CombatSkillHandler = MockHandler
    AlchemyHandler = MockHandler
    KarmaHandler = MockHandler
    AIDirectorHandler = MockHandler

logger = logging.getLogger(__name__)

# 模拟角色类
class MockCharacter:
    def __init__(self):
        self.name = "测试修仙者"
        self.key = "test_character"
        self.id = 1

# 全局Handler实例
_handlers = {}
_character = None

def get_handlers():
    """获取或初始化Handler实例"""
    global _handlers, _character
    
    if not _handlers:
        try:
            _character = MockCharacter()
            
            # 创建所有Handler
            _handlers['cultivation'] = CultivationHandler(_character)
            _handlers['combat'] = CombatSkillHandler(_character)
            _handlers['alchemy'] = AlchemyHandler(_character)
            _handlers['karma'] = KarmaHandler(_character)
            _handlers['ai_director'] = AIDirectorHandler(_character)
            
            # 初始化AI导演Handler
            if hasattr(_handlers['ai_director'], 'initialize'):
                _handlers['ai_director'].initialize()
            
            logger.info("✅ 所有Handler初始化成功")
        except Exception as e:
            logger.error(f"❌ Handler初始化失败: {e}")
            # 使用模拟Handler
            _handlers = {
                'cultivation': MockHandler(),
                'combat': MockHandler(),
                'alchemy': MockHandler(),
                'karma': MockHandler(),
                'ai_director': MockHandler()
            }
    
    return _handlers

def api_response(success=True, data=None, error=None):
    """标准API响应格式"""
    response = {"success": success}
    if data is not None:
        response["data"] = data
    if error is not None:
        response["error"] = error
    return JsonResponse(response)

# 修仙系统API
@csrf_exempt
@require_http_methods(["GET"])
def cultivation_realm(request):
    """获取当前境界"""
    try:
        handlers = get_handlers()
        result = handlers['cultivation'].get_current_realm()
        return api_response(data=result)
    except Exception as e:
        logger.error(f"获取境界失败: {e}")
        return api_response(success=False, error=str(e))

@csrf_exempt
@require_http_methods(["GET"])
def cultivation_progress(request):
    """获取修炼进度"""
    try:
        handlers = get_handlers()
        result = handlers['cultivation'].get_cultivation_progress()
        return api_response(data=result)
    except Exception as e:
        logger.error(f"获取修炼进度失败: {e}")
        return api_response(success=False, error=str(e))

@csrf_exempt
@require_http_methods(["POST"])
def cultivation_cultivate(request):
    """开始修炼"""
    try:
        handlers = get_handlers()
        result = handlers['cultivation'].start_cultivation()
        return api_response(data=result)
    except Exception as e:
        logger.error(f"开始修炼失败: {e}")
        return api_response(success=False, error=str(e))

@csrf_exempt
@require_http_methods(["POST"])
def cultivation_breakthrough(request):
    """尝试突破"""
    try:
        handlers = get_handlers()
        result = handlers['cultivation'].attempt_breakthrough()
        return api_response(data=result)
    except Exception as e:
        logger.error(f"尝试突破失败: {e}")
        return api_response(success=False, error=str(e))

# 战斗技能系统API
@csrf_exempt
@require_http_methods(["GET"])
def combat_available_skills(request):
    """获取可用技能"""
    try:
        handlers = get_handlers()
        result = handlers['combat'].get_available_skills()
        return api_response(data=result)
    except Exception as e:
        logger.error(f"获取可用技能失败: {e}")
        return api_response(success=False, error=str(e))

@csrf_exempt
@require_http_methods(["GET"])
def combat_learned_skills(request):
    """获取已学技能"""
    try:
        handlers = get_handlers()
        result = handlers['combat'].get_learned_skills()
        return api_response(data=result)
    except Exception as e:
        logger.error(f"获取已学技能失败: {e}")
        return api_response(success=False, error=str(e))

@csrf_exempt
@require_http_methods(["POST"])
def combat_learn_skill(request):
    """学习技能"""
    try:
        data = json.loads(request.body)
        skill_name = data.get('skill_name')
        
        handlers = get_handlers()
        result = handlers['combat'].learn_skill(skill_name)
        return api_response(data=result)
    except Exception as e:
        logger.error(f"学习技能失败: {e}")
        return api_response(success=False, error=str(e))

# 炼丹系统API
@csrf_exempt
@require_http_methods(["GET"])
def alchemy_recipes(request):
    """获取配方"""
    try:
        handlers = get_handlers()
        result = handlers['alchemy'].get_available_recipes()
        return api_response(data=result)
    except Exception as e:
        logger.error(f"获取配方失败: {e}")
        return api_response(success=False, error=str(e))

@csrf_exempt
@require_http_methods(["GET"])
def alchemy_materials(request):
    """获取材料"""
    try:
        handlers = get_handlers()
        result = handlers['alchemy'].get_materials()
        return api_response(data=result)
    except Exception as e:
        logger.error(f"获取材料失败: {e}")
        return api_response(success=False, error=str(e))

@csrf_exempt
@require_http_methods(["POST"])
def alchemy_add_material(request):
    """添加材料"""
    try:
        data = json.loads(request.body)
        name = data.get('name')
        amount = data.get('amount', 1)
        
        handlers = get_handlers()
        result = handlers['alchemy'].add_material(name, amount)
        return api_response(data=result)
    except Exception as e:
        logger.error(f"添加材料失败: {e}")
        return api_response(success=False, error=str(e))

# 因果系统API
@csrf_exempt
@require_http_methods(["GET"])
def karma_status(request):
    """获取因果状态"""
    try:
        handlers = get_handlers()
        result = handlers['karma'].get_karma_status()
        return api_response(data=result)
    except Exception as e:
        logger.error(f"获取因果状态失败: {e}")
        return api_response(success=False, error=str(e))

@csrf_exempt
@require_http_methods(["POST"])
def karma_record(request):
    """记录因果"""
    try:
        data = json.loads(request.body)
        karma_type = data.get('karma_type')
        amount = data.get('amount', 1)
        description = data.get('description', '')
        
        handlers = get_handlers()
        if karma_type == 'good':
            result = handlers['karma'].record_good_karma(amount, description)
        else:
            result = handlers['karma'].record_evil_karma(amount, description)
        return api_response(data=result)
    except Exception as e:
        logger.error(f"记录因果失败: {e}")
        return api_response(success=False, error=str(e))

# AI导演系统API
@csrf_exempt
@require_http_methods(["GET"])
def ai_director_story_status(request):
    """获取故事状态"""
    try:
        handlers = get_handlers()
        result = handlers['ai_director'].get_story_status()
        return api_response(data=result)
    except Exception as e:
        logger.error(f"获取故事状态失败: {e}")
        return api_response(success=False, error=str(e))

@csrf_exempt
@require_http_methods(["GET"])
def ai_director_world_state(request):
    """获取世界状态"""
    try:
        handlers = get_handlers()
        result = handlers['ai_director'].get_world_state()
        return api_response(data=result)
    except Exception as e:
        logger.error(f"获取世界状态失败: {e}")
        return api_response(success=False, error=str(e))

@csrf_exempt
@require_http_methods(["POST"])
def ai_director_update_context(request):
    """更新上下文"""
    try:
        data = json.loads(request.body)
        context = data.get('context', '')
        
        handlers = get_handlers()
        result = handlers['ai_director'].update_context(context)
        return api_response(data=result)
    except Exception as e:
        logger.error(f"更新上下文失败: {e}")
        return api_response(success=False, error=str(e))
