# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Evennia game directory - a Python-based MUD (Multi-User Dungeon) framework game project. Evennia is a library for creating text-based multiplayer online games with web client capabilities.

## Essential Commands

### Server Management
- `evennia migrate` - Initialize/update database schema (must be run before first start)
- `evennia start` - Start the game server (both server and portal processes)
- `evennia stop` - Stop the server and portal
- `evennia reload` - Restart server in reload mode (preserves player connections)
- `evennia reboot` - Full shutdown and restart of server and portal
- `evennia status` - Check if server and portal are running
- `evennia info` - Show server and portal port information

### Development Commands
- `evennia shell` - Access Django shell with Evennia environment
- `evennia test` - Run test suite (uses Django's test framework)
- `evennia dbshell` - Access database shell
- `py` (in-game) - Execute Python code in-game (requires developer permissions)

### Database and Content Management
- `@batchprocess <filename.ev>` - Load batch command files for world building
- `@batchcode <filename.py>` - Execute Python batch files
- `spawn <prototype>` - Create objects from prototypes
- `@reload` - Reload server modules without restart

### Logs and Debugging
- `evennia --log` - Tail server logs to stdout
- Logs are stored in `server/logs/`: `server.log`, `portal.log`, `http_requests.log`

## Architecture Overview

### Core Directory Structure

**typeclasses/** - Game entity definitions
- `characters.py` - Player character logic (inherits from DefaultCharacter)
- `objects.py` - Base object class with ObjectParent mixin for shared functionality
- `rooms.py` - Room/location definitions
- `exits.py` - Exit/movement connections between rooms
- `accounts.py` - Player account management
- `scripts.py` - Persistent background processes
- `channels.py` - Communication channels

**commands/** - Command implementations
- `command.py` - Base Command class for all game commands
- `default_cmdsets.py` - Default command sets that define available commands
- Commands inherit from BaseCommand and implement `func()` method
- MuxCommand parsing available for complex argument handling

**server/conf/** - Core configuration
- `settings.py` - Main game settings (inherits from evennia.settings_default)
- `secret_settings.py` - Private/sensitive configuration
- Connection screens, input functions, lock functions, web plugins

**world/** - Game content and systems
- `prototypes.py` - Object templates for spawning (module-based prototypes)
- `batch_cmds.ev` - Batch command files for world building
- `help_entries.py` - Custom help system entries
- Custom game mechanics, economy systems, combat code

**web/** - Web interface components
- `urls.py` - URL routing for web views
- `templates/` - HTML templates with Django template tags
- `static/` - CSS, JavaScript, images
- `webclient/` - Web-based MUD client interface
- `website/` - Main website views and templates
- `admin/` - Custom admin interface extensions

### Key Architectural Concepts

**Typeclass System**: Evennia uses a typeclass system where game entities (Objects, Characters, Rooms) inherit from base classes but store data in the database. Each instance can have its own typeclass while sharing common functionality.

**ObjectParent Mixin**: Located in `typeclasses/objects.py`, this mixin provides shared functionality for all location-based entities. Add methods here to affect all Objects, Characters, Rooms, and Exits.

**Command System**: Commands are parsed and executed through cmdsets. Each command has `parse()` for argument processing and `func()` for execution. MuxCommand provides advanced parsing for complex syntax.

**Prototype System**: Objects can be created from prototypes defined in `world/prototypes.py` or stored in database. Prototypes support inheritance and allow rapid content creation.

**Attribute System**: Game entities store data using Attributes (persistent) and NAttributes (non-persistent). Access via `obj.db.attrname` and `obj.ndb.attrname`.

**Permission and Lock System**: Fine-grained access control using lock strings and permission hierarchies.

### Web Architecture

The web interface follows Django patterns:
1. URLs in `web/urls.py` route to view functions
2. Views prepare data and render templates
3. Templates in `web/templates/` use Django template syntax
4. Static files served from `web/static/`
5. Webclient uses WebSocket connection to Portal for real-time communication

### Testing and Development

Evennia uses Django's testing framework. Tests should be placed in appropriate directories following Django conventions. The `py` command allows in-game Python execution for rapid prototyping and debugging.

### Default Ports
- MUD client connection: `localhost:4000`
- Web client: `http://localhost:4001`
- Web admin: `http://localhost:4001/admin`

## Development Notes

- Always run `evennia migrate` before starting the server for the first time
- Use `evennia reload` during development to apply code changes without disconnecting players
- The `ObjectParent` mixin in `typeclasses/objects.py` affects ALL game entities
- Settings in `secret_settings.py` override those in `settings.py`
- Batch files (`.ev`) use special comment syntax for command separation
- Prototypes support inheritance and callable values for dynamic content