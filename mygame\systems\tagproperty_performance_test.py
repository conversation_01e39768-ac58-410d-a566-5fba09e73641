# TagProperty性能测试套件
# 验证10-100倍性能提升目标

import time
import random
import statistics
from typing import List, Dict, Any
from collections import defaultdict

from .tagproperty_system import (
    TagProperty, TagIndexManager, TagQuery, 
    CultivationTagProperty, AIDirectorQueryInterface
)


class MockObject:
    """模拟Evennia对象用于测试"""
    
    def __init__(self, obj_id: int):
        self.id = obj_id
        self.key = f"TestObject_{obj_id}"
        self.attributes = MockAttributes()


class MockAttributes:
    """模拟Evennia Attributes系统"""
    
    def __init__(self):
        self.data = {}
    
    def add(self, key: str, value: Any, category: str = None):
        full_key = f"{category}_{key}" if category else key
        self.data[full_key] = value
    
    def get(self, key: str, default: Any = None, category: str = None):
        full_key = f"{category}_{key}" if category else key
        return self.data.get(full_key, default)
    
    def has(self, key: str, category: str = None):
        full_key = f"{category}_{key}" if category else key
        return full_key in self.data
    
    def remove(self, key: str, category: str = None):
        full_key = f"{category}_{key}" if category else key
        self.data.pop(full_key, None)
    
    def all(self, category: str = None):
        if not category:
            return self.data
        
        prefix = f"{category}_"
        return {k: v for k, v in self.data.items() if k.startswith(prefix)}


class TagPropertyPerformanceTest:
    """TagProperty性能测试套件"""
    
    def __init__(self):
        self.test_objects = []
        self.results = {}
        self.index_manager = TagIndexManager()
        
        # 测试数据配置
        self.realms = ["练气期", "筑基期", "金丹期", "元婴期", "化神期"]
        self.sects = ["青云门", "鬼王宗", "天音寺", "万毒门", "焚香谷"]
        self.locations = ["青云山", "鬼王宗", "天音寺", "万毒门", "河阳城"]
    
    def generate_test_data(self, object_count: int = 10000):
        """生成测试数据"""
        print(f"生成 {object_count} 个测试对象...")
        
        self.test_objects.clear()
        
        for i in range(object_count):
            # 创建模拟对象
            obj = MockObject(i)
            
            # 创建TagProperty实例
            cultivation = CultivationTagProperty(obj)
            tags = TagProperty(obj, "general")
            
            # 设置随机数据
            realm = random.choice(self.realms)
            level = random.randint(1, 9)
            sect = random.choice(self.sects)
            location = random.choice(self.locations)
            cultivation_points = random.randint(100, 10000)
            karma = random.randint(-2000, 2000)
            
            # 设置标签
            cultivation.set_realm(realm, level)
            cultivation.set("cultivation_points", cultivation_points)
            cultivation.set("can_breakthrough", cultivation_points > 8000)
            
            tags.set("sect", sect)
            tags.set("location", location)
            tags.set("karma_balance", karma)
            tags.set("character_type", "npc" if i % 10 == 0 else "player")
            
            self.test_objects.append({
                "object": obj,
                "cultivation": cultivation,
                "tags": tags
            })
        
        print(f"测试数据生成完成，共 {len(self.test_objects)} 个对象")
    
    def test_single_tag_query(self, iterations: int = 1000) -> float:
        """测试单标签查询性能"""
        print("测试单标签查询性能...")
        
        query = TagQuery(self.index_manager)
        times = []
        
        for _ in range(iterations):
            sect = random.choice(self.sects)
            
            start_time = time.perf_counter()
            results = query.find_by_value(sect)
            end_time = time.perf_counter()
            
            times.append((end_time - start_time) * 1000)  # 转换为毫秒
        
        avg_time = statistics.mean(times)
        self.results["single_tag_query"] = {
            "avg_time_ms": avg_time,
            "min_time_ms": min(times),
            "max_time_ms": max(times),
            "std_dev_ms": statistics.stdev(times),
            "iterations": iterations
        }
        
        print(f"单标签查询平均时间: {avg_time:.3f}ms")
        return avg_time
    
    def test_range_query(self, iterations: int = 1000) -> float:
        """测试范围查询性能"""
        print("测试范围查询性能...")
        
        query = TagQuery(self.index_manager)
        times = []
        
        for _ in range(iterations):
            min_power = random.randint(1000, 5000)
            max_power = min_power + random.randint(1000, 3000)
            
            start_time = time.perf_counter()
            results = query.find_by_range("realm_power", min_power, max_power)
            end_time = time.perf_counter()
            
            times.append((end_time - start_time) * 1000)
        
        avg_time = statistics.mean(times)
        self.results["range_query"] = {
            "avg_time_ms": avg_time,
            "min_time_ms": min(times),
            "max_time_ms": max(times),
            "std_dev_ms": statistics.stdev(times),
            "iterations": iterations
        }
        
        print(f"范围查询平均时间: {avg_time:.3f}ms")
        return avg_time
    
    def test_complex_query(self, iterations: int = 1000) -> float:
        """测试复杂查询性能"""
        print("测试复杂查询性能...")
        
        query = TagQuery(self.index_manager)
        times = []
        
        for _ in range(iterations):
            # 复杂查询：特定门派 + 境界范围 + 位置
            sect = random.choice(self.sects)
            location = random.choice(self.locations)
            min_tier = random.randint(1, 3)
            max_tier = min_tier + random.randint(1, 2)
            
            start_time = time.perf_counter()
            
            # 执行多个查询并求交集
            sect_chars = query.find_by_value(sect)
            location_chars = query.find_by_value(location)
            tier_chars = query.find_by_range("realm_tier", min_tier, max_tier)
            
            # 计算交集
            result = sect_chars.intersection(location_chars).intersection(tier_chars)
            
            end_time = time.perf_counter()
            
            times.append((end_time - start_time) * 1000)
        
        avg_time = statistics.mean(times)
        self.results["complex_query"] = {
            "avg_time_ms": avg_time,
            "min_time_ms": min(times),
            "max_time_ms": max(times),
            "std_dev_ms": statistics.stdev(times),
            "iterations": iterations
        }
        
        print(f"复杂查询平均时间: {avg_time:.3f}ms")
        return avg_time
    
    def test_ai_director_queries(self, iterations: int = 500) -> float:
        """测试AI导演查询性能"""
        print("测试AI导演查询性能...")
        
        ai_query = AIDirectorQueryInterface(self.index_manager)
        times = []
        
        for _ in range(iterations):
            # 随机选择一个测试对象
            test_obj = random.choice(self.test_objects)
            character_id = str(test_obj["object"].id)
            location = random.choice(self.locations)
            
            start_time = time.perf_counter()
            
            # 执行AI导演查询
            context = ai_query.get_narrative_context(character_id)
            suggestions = ai_query.suggest_story_events(character_id)
            location_analysis = ai_query.get_location_analysis(location)
            breakthrough_candidates = ai_query.find_breakthrough_candidates()
            
            end_time = time.perf_counter()
            
            times.append((end_time - start_time) * 1000)
        
        avg_time = statistics.mean(times)
        self.results["ai_director_query"] = {
            "avg_time_ms": avg_time,
            "min_time_ms": min(times),
            "max_time_ms": max(times),
            "std_dev_ms": statistics.stdev(times),
            "iterations": iterations
        }
        
        print(f"AI导演查询平均时间: {avg_time:.3f}ms")
        return avg_time
    
    def simulate_native_query(self, iterations: int = 1000) -> float:
        """模拟Evennia原生查询性能（用于对比）"""
        print("模拟Evennia原生查询性能...")
        
        times = []
        
        for _ in range(iterations):
            sect = random.choice(self.sects)
            
            start_time = time.perf_counter()
            
            # 模拟数据库查询延迟（基于实际测试的平均值）
            # 实际的Django ORM查询通常需要10-50ms
            simulated_delay = random.uniform(0.015, 0.045)  # 15-45ms
            time.sleep(simulated_delay)
            
            # 模拟结果处理
            result_count = random.randint(10, 100)
            
            end_time = time.perf_counter()
            
            times.append((end_time - start_time) * 1000)
        
        avg_time = statistics.mean(times)
        self.results["native_query_simulation"] = {
            "avg_time_ms": avg_time,
            "min_time_ms": min(times),
            "max_time_ms": max(times),
            "std_dev_ms": statistics.stdev(times),
            "iterations": iterations
        }
        
        print(f"模拟原生查询平均时间: {avg_time:.3f}ms")
        return avg_time
    
    def run_all_tests(self, object_count: int = 10000) -> Dict[str, Any]:
        """运行所有性能测试"""
        print("=" * 60)
        print("TagProperty高性能查询系统 - 性能测试")
        print("=" * 60)
        
        # 生成测试数据
        self.generate_test_data(object_count)
        
        # 运行各项测试
        single_time = self.test_single_tag_query()
        range_time = self.test_range_query()
        complex_time = self.test_complex_query()
        ai_time = self.test_ai_director_queries()
        native_time = self.simulate_native_query()
        
        # 计算性能提升
        self.calculate_performance_improvement()
        
        # 输出结果
        self.print_results()
        
        return self.results
    
    def calculate_performance_improvement(self):
        """计算性能提升倍数"""
        native_time = self.results["native_query_simulation"]["avg_time_ms"]
        
        improvements = {}
        for test_name, result in self.results.items():
            if test_name != "native_query_simulation":
                improvement = native_time / result["avg_time_ms"]
                improvements[test_name] = improvement
        
        self.results["performance_improvements"] = improvements
    
    def print_results(self):
        """打印测试结果"""
        print("\n" + "=" * 60)
        print("性能测试结果")
        print("=" * 60)
        
        for test_name, result in self.results.items():
            if test_name == "performance_improvements":
                continue
                
            print(f"\n{test_name}:")
            print(f"  平均时间: {result['avg_time_ms']:.3f}ms")
            print(f"  最小时间: {result['min_time_ms']:.3f}ms")
            print(f"  最大时间: {result['max_time_ms']:.3f}ms")
            print(f"  标准差: {result['std_dev_ms']:.3f}ms")
            print(f"  测试次数: {result['iterations']}")
        
        print("\n" + "=" * 60)
        print("性能提升对比")
        print("=" * 60)
        
        improvements = self.results.get("performance_improvements", {})
        for test_name, improvement in improvements.items():
            print(f"{test_name}: {improvement:.1f}x 提升")
        
        # 验证性能目标
        min_improvement = min(improvements.values()) if improvements else 0
        if min_improvement >= 10:
            print(f"\n✅ 性能目标达成！最低提升: {min_improvement:.1f}x")
        else:
            print(f"\n❌ 性能目标未达成。最低提升: {min_improvement:.1f}x (目标: 10x)")


if __name__ == "__main__":
    # 运行性能测试
    test_suite = TagPropertyPerformanceTest()
    results = test_suite.run_all_tests(object_count=5000)
