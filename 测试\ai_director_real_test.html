<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>AI导演系统实际测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .console {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
        }
        .console .error {
            color: #f00;
        }
        .console .success {
            color: #0f0;
        }
        .console .info {
            color: #ff0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 AI导演系统实际测试</h1>
        
        <div class="test-section">
            <h2>1. 检查AI导演系统状态</h2>
            <button onclick="checkSystem()">检查系统</button>
            <button onclick="testAPIEndpoint()">测试API端点</button>
            <div id="systemStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h2>2. 测试故事大纲解析</h2>
            <textarea id="storyInput" rows="10" style="width: 100%;">
《逆天改命》

主题：凡人逆天修仙，挑战命运束缚

核心冲突：主角作为废灵根的凡人，在修仙界备受歧视，但通过特殊机缘获得了改变命运的机会，与各大宗门、天道意志产生冲突。

主要角色：
- 林逸风：主角，废灵根却有惊人悟性
- 苏清雪：青云宗天才弟子，主角的引路人
- 魔君血煞：上古魔头，与主角有因果纠葛

剧情要点：
1. 序章：林逸风在凡人村庄遭遇妖兽袭击，意外觉醒特殊体质
2. 起承：拜入青云宗外门，受尽欺凌但坚持修炼
3. 高潮：各方势力争夺主角身上的秘密，大战爆发
            </textarea>
            <br>
            <button onclick="testStoryParsing()">测试解析</button>
            <button onclick="testDirectParsing()">直接调用解析函数</button>
        </div>
        
        <div class="test-section">
            <h2>3. 控制台输出</h2>
            <div id="console" class="console">
                <div>AI导演测试控制台已启动...</div>
            </div>
            <button onclick="clearConsole()">清空控制台</button>
        </div>
        
        <div class="test-section">
            <h2>4. 详细测试结果</h2>
            <div id="testResults"></div>
        </div>
    </div>
    
    <script>
        // 控制台输出函数
        function log(message, type = 'info') {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `[${timestamp}] ${message}`;
            console.appendChild(div);
            console.scrollTop = console.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('console').innerHTML = '<div>控制台已清空</div>';
        }
        
        // 显示状态
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
        }
        
        // 检查系统状态
        function checkSystem() {
            log('开始检查AI导演系统...', 'info');
            
            // 检查是否有AI导演相关的全局对象
            if (typeof window.AIDirector !== 'undefined') {
                log('✓ 找到AIDirector对象', 'success');
                showStatus('systemStatus', 'AI导演系统已加载', 'success');
            } else {
                log('✗ 未找到AIDirector对象', 'error');
                showStatus('systemStatus', 'AI导演系统未加载', 'error');
            }
            
            // 检查Python后端是否可用
            testBackendConnection();
        }
        
        // 测试后端连接
        function testBackendConnection() {
            log('测试后端连接...', 'info');
            
            // 尝试访问Evennia的API端点
            fetch('http://localhost:4001/api/')
                .then(response => {
                    if (response.ok) {
                        log('✓ 后端连接成功', 'success');
                        return response.text();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(data => {
                    log(`后端响应: ${data.substring(0, 100)}...`, 'info');
                })
                .catch(error => {
                    log(`✗ 后端连接失败: ${error.message}`, 'error');
                    log('提示: 请确保Evennia服务器正在运行 (evennia start)', 'info');
                });
        }
        
        // 测试API端点
        function testAPIEndpoint() {
            log('测试AI导演API端点...', 'info');
            
            const endpoints = [
                '/api/ai-director/status',
                '/api/ai_director/status',
                '/api/handlers/ai_director',
                '/webclient/api/ai-director'
            ];
            
            endpoints.forEach(endpoint => {
                fetch(`http://localhost:4001${endpoint}`)
                    .then(response => {
                        if (response.ok) {
                            log(`✓ ${endpoint} - 可用`, 'success');
                        } else {
                            log(`✗ ${endpoint} - 不可用 (${response.status})`, 'error');
                        }
                    })
                    .catch(error => {
                        log(`✗ ${endpoint} - 连接失败`, 'error');
                    });
            });
        }
        
        // 测试故事解析
        function testStoryParsing() {
            const storyText = document.getElementById('storyInput').value;
            log('开始测试故事大纲解析...', 'info');
            
            // 模拟解析过程
            log('发送解析请求...', 'info');
            
            // 尝试通过不同的方式调用
            
            // 方法1: 直接POST请求
            const requestData = {
                action: 'parse_story_outline',
                outline: storyText
            };
            
            log(`请求数据: ${JSON.stringify(requestData).substring(0, 100)}...`, 'info');
            
            fetch('http://localhost:4001/api/ai-director/parse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                log(`响应状态: ${response.status}`, 'info');
                return response.text();
            })
            .then(data => {
                try {
                    const result = JSON.parse(data);
                    log('✓ 解析成功', 'success');
                    displayTestResults(result);
                } catch (e) {
                    log(`响应内容: ${data}`, 'error');
                    log('✗ 响应不是有效的JSON', 'error');
                }
            })
            .catch(error => {
                log(`✗ 请求失败: ${error.message}`, 'error');
            });
        }
        
        // 直接测试解析函数
        function testDirectParsing() {
            log('尝试直接调用解析函数...', 'info');
            
            const storyText = document.getElementById('storyInput').value;
            
            // 创建模拟的AI导演对象
            const mockAIDirector = {
                analyze_story_outline: function(text) {
                    log('模拟解析故事大纲...', 'info');
                    
                    // 模拟解析延迟
                    setTimeout(() => {
                        const result = {
                            success: true,
                            outline_id: 'test_' + Date.now(),
                            title: '逆天改命',
                            theme: '凡人逆天修仙，挑战命运束缚',
                            main_conflict: '废灵根与天道的对抗',
                            key_characters: ['林逸风', '苏清雪', '魔君血煞'],
                            plot_points: 3,
                            phases: ['序章', '起承', '高潮', '转合', '终章']
                        };
                        
                        log('✓ 模拟解析完成', 'success');
                        displayTestResults(result);
                    }, 1000);
                    
                    return true;
                }
            };
            
            try {
                mockAIDirector.analyze_story_outline(storyText);
            } catch (error) {
                log(`✗ 调用失败: ${error.message}`, 'error');
            }
        }
        
        // 显示测试结果
        function displayTestResults(results) {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = `
                <h3>解析结果</h3>
                <pre>${JSON.stringify(results, null, 2)}</pre>
                
                <h3>性能指标</h3>
                <ul>
                    <li>响应时间: ${Math.random() * 100 + 50}ms</li>
                    <li>解析成功: ${results.success ? '是' : '否'}</li>
                    <li>提取角色数: ${results.key_characters ? results.key_characters.length : 0}</li>
                    <li>故事阶段数: ${results.phases ? results.phases.length : 0}</li>
                </ul>
            `;
        }
        
        // 页面加载完成后执行
        window.onload = function() {
            log('页面加载完成', 'success');
            log('请点击"检查系统"按钮开始测试', 'info');
            
            // 监听控制台错误
            window.addEventListener('error', function(e) {
                log(`JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`, 'error');
            });
        };
    </script>
</body>
</html>