#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单页面 Playwright自动化测试
测试基础Web界面功能
"""

import asyncio
import json
from playwright.async_api import async_playwright
import time

class SimplePlaywrightTest:
    def __init__(self):
        self.base_url = "http://localhost:4005"
        self.test_results = []
        
    async def run_tests(self):
        """运行所有测试"""
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(headless=False)  # 设置为False以便观察测试过程
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                print("🚀 开始简单页面 Playwright测试...")
                
                # 导航到测试页面
                await self.navigate_to_test_page(page)
                
                # 等待页面加载
                await page.wait_for_timeout(2000)
                
                # 测试基础功能
                await self.test_basic_functions(page)
                
                # 测试系统信息
                await self.test_system_info(page)
                
                # 生成测试报告
                self.generate_report()
                
            except Exception as e:
                print(f"❌ 测试过程中发生错误: {e}")
                self.test_results.append({
                    "test": "整体测试",
                    "status": "失败",
                    "error": str(e)
                })
            finally:
                await browser.close()
    
    async def navigate_to_test_page(self, page):
        """导航到测试页面"""
        try:
            print("📍 导航到简单测试页面...")
            await page.goto(f"{self.base_url}/simple-test/")
            
            # 等待页面标题加载
            await page.wait_for_selector("h1", timeout=10000)
            title = await page.text_content("h1")
            
            if "简单测试页面" in title:
                print("✅ 成功加载测试页面")
                self.test_results.append({
                    "test": "页面加载",
                    "status": "成功",
                    "details": f"页面标题: {title}"
                })
            else:
                raise Exception(f"页面标题不正确: {title}")
                
        except Exception as e:
            print(f"❌ 页面加载失败: {e}")
            self.test_results.append({
                "test": "页面加载",
                "status": "失败",
                "error": str(e)
            })
            raise
    
    async def test_basic_functions(self, page):
        """测试基础功能"""
        print("\n🔧 测试基础功能...")
        
        # 测试基础功能按钮
        await self.click_and_verify(page, "testBasic()", "basic-result", "基础功能测试")
        
        # 测试时间功能
        await self.click_and_verify(page, "testTime()", "basic-result", "时间获取测试")
        
        # 测试随机数功能
        await self.click_and_verify(page, "testRandom()", "basic-result", "随机数生成测试")
    
    async def test_system_info(self, page):
        """测试系统信息功能"""
        print("\n💻 测试系统信息...")
        
        # 测试系统信息获取
        await self.click_and_verify(page, "getSystemInfo()", "system-result", "系统信息获取")
    
    async def click_and_verify(self, page, function_call, result_id, test_name):
        """执行JavaScript函数并验证结果"""
        try:
            # 执行JavaScript函数
            await page.evaluate(function_call)
            
            # 等待结果更新
            await page.wait_for_timeout(1000)
            
            # 获取结果
            result_element = await page.query_selector(f"#{result_id}")
            if result_element:
                result_text = await result_element.text_content()
                
                # 检查是否包含成功标识
                if result_text and ("✅" in result_text or "🕐" in result_text or "🎲" in result_text or "浏览器信息" in result_text):
                    print(f"  ✅ {test_name}: 成功")
                    self.test_results.append({
                        "test": test_name,
                        "status": "成功",
                        "response": result_text[:200] + "..." if len(result_text) > 200 else result_text
                    })
                else:
                    print(f"  ⚠️ {test_name}: 响应异常")
                    self.test_results.append({
                        "test": test_name,
                        "status": "警告",
                        "response": result_text[:200] + "..." if len(result_text) > 200 else result_text
                    })
            else:
                print(f"  ❌ {test_name}: 未找到结果元素")
                self.test_results.append({
                    "test": test_name,
                    "status": "失败",
                    "error": "未找到结果元素"
                })
                
        except Exception as e:
            print(f"  ❌ {test_name}: {e}")
            self.test_results.append({
                "test": test_name,
                "status": "失败",
                "error": str(e)
            })
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 简单页面 Playwright测试报告")
        print("="*60)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r["status"] == "成功"])
        failed_tests = len([r for r in self.test_results if r["status"] == "失败"])
        warning_tests = len([r for r in self.test_results if r["status"] == "警告"])
        
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests}")
        print(f"失败: {failed_tests}")
        print(f"警告: {warning_tests}")
        print(f"成功率: {(successful_tests/total_tests*100):.1f}%")
        
        print("\n详细结果:")
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "成功" else "❌" if result["status"] == "失败" else "⚠️"
            print(f"{status_icon} {result['test']}: {result['status']}")
            if "error" in result:
                print(f"   错误: {result['error']}")
            elif "response" in result:
                print(f"   响应: {result['response'][:100]}...")
        
        # 保存详细报告到文件
        with open("simple_playwright_test_report.json", "w", encoding="utf-8") as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: simple_playwright_test_report.json")
        
        if failed_tests == 0:
            print("\n🎉 所有测试通过！Web界面基础功能正常！")
            print("现在可以继续测试修仙Handler系统...")
        else:
            print(f"\n⚠️ 有 {failed_tests} 个测试失败，请检查相关功能。")

async def main():
    """主函数"""
    tester = SimplePlaywrightTest()
    await tester.run_tests()

if __name__ == "__main__":
    print("🧙‍♂️ 简单页面 Playwright自动化测试启动...")
    print("确保Evennia服务器正在运行在 http://localhost:4005")
    print("测试将在3秒后开始...")
    time.sleep(3)
    
    asyncio.run(main())
