#!/usr/bin/env python
"""
Playwright Web测试脚本 - 测试Handler生态组件化框架
"""

import asyncio
import time
from playwright.async_api import async_playwright


async def test_handler_ecosystem():
    """测试Handler生态系统"""
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            print("🚀 开始Playwright Web测试...")
            
            # 导航到测试页面
            print("📡 连接到测试服务器...")
            await page.goto("http://localhost:8000")
            
            # 等待页面加载
            await page.wait_for_load_state("networkidle")
            print("✓ 页面加载完成")
            
            # 检查页面标题
            title = await page.title()
            print(f"✓ 页面标题: {title}")
            
            # 测试创建角色
            print("\n🎭 测试创建角色...")
            await page.click('button:has-text("创建测试角色")')
            await page.wait_for_timeout(1000)
            
            # 检查结果
            results = await page.query_selector_all('.result')
            if results:
                latest_result = await results[0].inner_text()
                print(f"✓ 创建角色结果: {latest_result}")
            
            # 测试Handler功能
            print("\n🔧 测试Handler功能...")
            await page.click('button:has-text("测试Handler功能")')
            await page.wait_for_timeout(2000)
            
            # 检查Handler测试结果
            results = await page.query_selector_all('.result')
            if len(results) >= 2:
                handler_result = await results[0].inner_text()
                print(f"✓ Handler测试结果: {handler_result}")
            
            # 运行性能测试
            print("\n⚡ 运行性能测试...")
            await page.click('button:has-text("性能测试")')
            await page.wait_for_timeout(3000)
            
            # 检查性能测试结果
            results = await page.query_selector_all('.result')
            if len(results) >= 3:
                perf_result = await results[0].inner_text()
                print(f"✓ 性能测试结果: {perf_result}")
            
            # 运行内存清理测试
            print("\n🧹 运行内存清理测试...")
            await page.click('button:has-text("内存清理测试")')
            await page.wait_for_timeout(2000)
            
            # 检查清理测试结果
            results = await page.query_selector_all('.result')
            if len(results) >= 4:
                cleanup_result = await results[0].inner_text()
                print(f"✓ 清理测试结果: {cleanup_result}")
            
            # 刷新统计数据
            print("\n📊 刷新统计数据...")
            await page.click('button:has-text("刷新统计")')
            await page.wait_for_timeout(2000)
            
            # 检查内存统计
            memory_stats = await page.query_selector('#memory-stats')
            if memory_stats:
                stats_text = await memory_stats.inner_text()
                print(f"✓ 内存统计: {stats_text[:200]}...")
            
            # 检查事件历史
            event_history = await page.query_selector('#event-history')
            if event_history:
                events_text = await event_history.inner_text()
                print(f"✓ 事件历史: {events_text[:200]}...")
            
            # 截图保存测试结果
            await page.screenshot(path="handler_test_results.png")
            print("✓ 测试截图已保存: handler_test_results.png")
            
            # 等待一段时间让用户观察
            print("\n⏳ 等待5秒钟观察测试结果...")
            await page.wait_for_timeout(5000)
            
            print("\n🎉 Playwright Web测试完成！")
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")
            # 截图保存错误状态
            await page.screenshot(path="handler_test_error.png")
            
        finally:
            await browser.close()


async def test_api_endpoints():
    """测试API端点"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context()
        
        try:
            print("\n🔌 测试API端点...")
            
            # 测试创建角色API
            page = await context.new_page()
            response = await page.goto("http://localhost:8000/api/create_character?name=API测试角色")
            
            if response.status == 200:
                data = await response.json()
                print(f"✓ 创建角色API: {data.get('message', 'Success')}")
            else:
                print(f"❌ 创建角色API失败: {response.status}")
            
            # 测试Handler测试API
            response = await page.goto("http://localhost:8000/api/test_handlers")
            if response.status == 200:
                data = await response.json()
                print(f"✓ Handler测试API: {data.get('message', 'Success')}")
            else:
                print(f"❌ Handler测试API失败: {response.status}")
            
            # 测试内存统计API
            response = await page.goto("http://localhost:8000/api/memory_stats")
            if response.status == 200:
                data = await response.json()
                print(f"✓ 内存统计API: 当前Handler数量 {data.get('current_handler_count', 0)}")
            else:
                print(f"❌ 内存统计API失败: {response.status}")
            
            # 测试事件历史API
            response = await page.goto("http://localhost:8000/api/event_history")
            if response.status == 200:
                data = await response.json()
                event_count = len(data.get('events', []))
                print(f"✓ 事件历史API: 记录了 {event_count} 个事件")
            else:
                print(f"❌ 事件历史API失败: {response.status}")
            
            # 测试性能测试API
            response = await page.goto("http://localhost:8000/api/performance_test")
            if response.status == 200:
                data = await response.json()
                print(f"✓ 性能测试API: {data.get('message', 'Success')}")
                if 'performance' in data:
                    perf = data['performance']
                    print(f"  - 创建时间: {perf.get('creation_time', 'N/A')}")
                    print(f"  - 访问时间: {perf.get('access_time', 'N/A')}")
            else:
                print(f"❌ 性能测试API失败: {response.status}")
            
            # 测试清理测试API
            response = await page.goto("http://localhost:8000/api/cleanup_test")
            if response.status == 200:
                data = await response.json()
                cleaned = data.get('cleaned_count', 0)
                print(f"✓ 清理测试API: 清理了 {cleaned} 个Handler")
            else:
                print(f"❌ 清理测试API失败: {response.status}")
            
        except Exception as e:
            print(f"❌ API测试过程中出现错误: {e}")
            
        finally:
            await browser.close()


async def main():
    """主测试函数"""
    print("🎮 Handler生态组件化框架 Playwright Web测试")
    print("=" * 60)
    
    try:
        # 首先测试API端点
        await test_api_endpoints()
        
        # 然后进行完整的Web界面测试
        await test_handler_ecosystem()
        
        print("\n" + "=" * 60)
        print("🎉 所有Playwright测试完成！")
        print("✅ Handler生态组件化框架Web功能验证通过")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
