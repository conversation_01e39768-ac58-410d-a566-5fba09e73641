evennia@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo           自动关机状态检查
echo ==========================================
echo.

echo 📅 当前时间:
powershell -Command "Get-Date -Format 'yyyy-MM-dd HH:mm:ss'"
echo.

echo 🔍 检查关机任务状态...
echo.

REM 检查是否有活动的关机任务
powershell -Command "try { $proc = Get-Process | Where-Object {$_.ProcessName -eq 'shutdown'}; if($proc) { Write-Host '✅ 关机任务正在运行' -ForegroundColor Green } else { Write-Host '❌ 未检测到关机任务' -ForegroundColor Red } } catch { Write-Host '⚠️ 无法检查关机任务状态' -ForegroundColor Yellow }"

echo.
echo 💡 提示:
echo    - 如需取消关机: shutdown /a
echo    - 如需立即关机: shutdown /s /t 0
echo    - 如需重新设置: shutdown /s /t [秒数]
echo.

echo 🕚 预定关机时间: 23:30 (今晚11:30)
echo.

pause
