#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修仙MUD API URL配置
"""

from django.urls import path
from . import xiuxian_api

app_name = 'xiuxian_api'

urlpatterns = [
    # 修仙系统API
    path('cultivation/realm/', xiuxian_api.cultivation_realm, name='cultivation_realm'),
    path('cultivation/progress/', xiuxian_api.cultivation_progress, name='cultivation_progress'),
    path('cultivation/cultivate/', xiuxian_api.cultivation_cultivate, name='cultivation_cultivate'),
    path('cultivation/breakthrough/', xiuxian_api.cultivation_breakthrough, name='cultivation_breakthrough'),
    
    # 战斗技能系统API
    path('combat/available-skills/', xiuxian_api.combat_available_skills, name='combat_available_skills'),
    path('combat/learned-skills/', xiuxian_api.combat_learned_skills, name='combat_learned_skills'),
    path('combat/learn-skill/', xiuxian_api.combat_learn_skill, name='combat_learn_skill'),
    
    # 炼丹系统API
    path('alchemy/recipes/', xiuxian_api.alchemy_recipes, name='alchemy_recipes'),
    path('alchemy/materials/', xiuxian_api.alchemy_materials, name='alchemy_materials'),
    path('alchemy/add-material/', xiuxian_api.alchemy_add_material, name='alchemy_add_material'),
    
    # 因果系统API
    path('karma/status/', xiuxian_api.karma_status, name='karma_status'),
    path('karma/record/', xiuxian_api.karma_record, name='karma_record'),
    
    # AI导演系统API
    path('ai-director/story-status/', xiuxian_api.ai_director_story_status, name='ai_director_story_status'),
    path('ai-director/world-state/', xiuxian_api.ai_director_world_state, name='ai_director_world_state'),
    path('ai-director/update-context/', xiuxian_api.ai_director_update_context, name='ai_director_update_context'),
]
