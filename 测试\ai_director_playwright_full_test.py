#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统Playwright完整功能测试
使用全局配置进行真实Evennia环境的完整测试
"""

import asyncio
import json
import time
from playwright.async_api import async_playwright, expect

class AIDirectorPlaywrightTester:
    """AI导演系统Playwright测试器"""
    
    def __init__(self):
        self.base_url = "http://localhost:4001"
        self.test_results = []
        self.browser = None
        self.page = None
        self.playwright = None
    
    async def setup(self):
        """初始化浏览器"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=False,  # 显示浏览器窗口
                slow_mo=500      # 减慢操作速度以便观察
            )
            self.page = await self.browser.new_page()
            
            # 设置页面超时
            self.page.set_default_timeout(30000)
            
            print("✅ Playwright浏览器初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ Playwright初始化失败: {e}")
            return False
        
    async def teardown(self):
        """清理资源"""
        try:
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            print("✅ Playwright资源清理完成")
        except Exception as e:
            print(f"⚠️ 资源清理异常: {e}")
    
    def log_result(self, test_name, success, message="", details=None):
        """记录测试结果"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "details": details,
            "timestamp": time.strftime("%H:%M:%S")
        }
        self.test_results.append(result)
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
        if details:
            print(f"   详情: {details}")
    
    async def test_homepage_access(self):
        """测试主页访问"""
        try:
            print("\n🌐 测试Evennia主页访问...")
            await self.page.goto(self.base_url)
            await self.page.wait_for_load_state('networkidle')
            
            # 检查页面标题
            title = await self.page.title()
            
            # 检查页面内容
            content = await self.page.content()
            
            if "evennia" in title.lower() or "testgame" in title.lower():
                self.log_result("主页访问", True, f"页面标题: {title}")
                return True
            else:
                self.log_result("主页访问", False, f"意外的页面标题: {title}")
                return False
                
        except Exception as e:
            self.log_result("主页访问", False, f"访问失败: {str(e)}")
            return False
    
    async def test_webclient_access(self):
        """测试Web客户端访问"""
        try:
            print("\n🎮 测试Web客户端访问...")
            webclient_url = f"{self.base_url}/webclient/"
            await self.page.goto(webclient_url)
            await self.page.wait_for_load_state('networkidle')
            
            # 等待页面完全加载
            await self.page.wait_for_timeout(2000)
            
            # 检查游戏界面元素
            game_elements = [
                'input[type="text"]',
                '.msg',
                '#inputfield',
                '.webclient',
                'input'
            ]
            
            found_elements = []
            for selector in game_elements:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=3000)
                    if element:
                        found_elements.append(selector)
                except:
                    pass
            
            if found_elements:
                self.log_result("Web客户端访问", True, f"找到游戏元素: {found_elements}")
                return True
            else:
                # 截图保存以便调试
                await self.page.screenshot(path="测试/webclient_debug.png")
                self.log_result("Web客户端访问", False, "未找到游戏界面元素，已保存截图")
                return False
                
        except Exception as e:
            self.log_result("Web客户端访问", False, f"访问失败: {str(e)}")
            return False
    
    async def test_api_endpoints(self):
        """测试AI导演API端点"""
        try:
            print("\n📡 测试AI导演API端点...")
            
            # 测试状态API
            status_response = await self.page.evaluate("""
                async () => {
                    try {
                        const response = await fetch('/api/ai-director/status/');
                        const data = await response.json();
                        return { success: true, status: response.status, data: data };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                }
            """)
            
            if status_response.get('success') and status_response.get('status') == 200:
                data = status_response.get('data', {})
                self.log_result("API状态端点", True, 
                              f"状态: {data.get('status', '未知')}, 可用: {data.get('available', False)}")
            else:
                self.log_result("API状态端点", False, 
                              f"请求失败: {status_response.get('error', '未知错误')}")
                return False
            
            # 测试故事解析API
            parse_response = await self.page.evaluate("""
                async () => {
                    try {
                        const response = await fetch('/api/ai-director/parse-outline/', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                outline_text: '《逆天改命》主题：凡人逆天修仙，挑战命运束缚'
                            })
                        });
                        const data = await response.json();
                        return { success: true, status: response.status, data: data };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                }
            """)
            
            if parse_response.get('success') and parse_response.get('status') == 200:
                data = parse_response.get('data', {})
                if data.get('success'):
                    self.log_result("故事解析API", True, 
                                  f"解析成功: {data.get('title', '未知标题')}")
                else:
                    self.log_result("故事解析API", False, 
                                  f"解析失败: {data.get('error', '未知错误')}")
            else:
                self.log_result("故事解析API", False, 
                              f"请求失败: {parse_response.get('error', '未知错误')}")
            
            # 测试决策生成API
            decision_response = await self.page.evaluate("""
                async () => {
                    try {
                        const response = await fetch('/api/ai-director/make-decision/', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                event_type: 'CultivationBreakthrough',
                                character: 'testuser',
                                description: '玩家突破到筑基期',
                                location: '青云峰'
                            })
                        });
                        const data = await response.json();
                        return { success: true, status: response.status, data: data };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                }
            """)
            
            if decision_response.get('success') and decision_response.get('status') == 200:
                data = decision_response.get('data', {})
                if data.get('success'):
                    self.log_result("决策生成API", True, 
                                  f"决策类型: {data.get('decision_type', '未知')}")
                else:
                    self.log_result("决策生成API", False, 
                                  f"决策失败: {data.get('error', '未知错误')}")
            else:
                self.log_result("决策生成API", False, 
                              f"请求失败: {decision_response.get('error', '未知错误')}")
            
            return True
            
        except Exception as e:
            self.log_result("API端点测试", False, f"测试异常: {str(e)}")
            return False
    
    async def test_performance_benchmark(self):
        """测试性能基准"""
        try:
            print("\n⚡ 测试API性能基准...")
            
            # 执行多次API调用测试性能
            performance_result = await self.page.evaluate("""
                async () => {
                    const results = [];
                    
                    for (let i = 0; i < 5; i++) {
                        const start = Date.now();
                        try {
                            const response = await fetch('/api/ai-director/make-decision/', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    event_type: 'PerformanceTest',
                                    character: 'benchmark',
                                    test_id: i
                                })
                            });
                            const end = Date.now();
                            const data = await response.json();
                            
                            results.push({
                                success: response.status === 200,
                                time: end - start,
                                response: data
                            });
                        } catch (error) {
                            results.push({
                                success: false,
                                time: Date.now() - start,
                                error: error.message
                            });
                        }
                        
                        // 短暂延迟
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }
                    
                    return results;
                }
            """)
            
            if performance_result:
                successful_tests = [r for r in performance_result if r.get('success')]
                if successful_tests:
                    times = [r['time'] for r in successful_tests]
                    avg_time = sum(times) / len(times)
                    max_time = max(times)
                    min_time = min(times)
                    
                    performance_ok = avg_time < 2000  # 2秒内算正常
                    
                    self.log_result("性能基准测试", performance_ok,
                                  f"成功: {len(successful_tests)}/5, 平均: {avg_time:.1f}ms, 范围: {min_time:.1f}-{max_time:.1f}ms")
                    return performance_ok
                else:
                    self.log_result("性能基准测试", False, "所有性能测试都失败了")
                    return False
            else:
                self.log_result("性能基准测试", False, "无法获取性能测试结果")
                return False
                
        except Exception as e:
            self.log_result("性能基准测试", False, f"测试异常: {str(e)}")
            return False
    
    async def test_interactive_features(self):
        """测试交互功能"""
        try:
            print("\n🎭 测试交互功能...")
            
            # 回到webclient页面
            webclient_url = f"{self.base_url}/webclient/"
            await self.page.goto(webclient_url)
            await self.page.wait_for_load_state('networkidle')
            await self.page.wait_for_timeout(2000)
            
            # 尝试找到输入框并输入命令
            input_selectors = [
                'input[type="text"]',
                '#inputfield',
                '.input',
                'input'
            ]
            
            input_element = None
            for selector in input_selectors:
                try:
                    input_element = await self.page.wait_for_selector(selector, timeout=3000)
                    if input_element:
                        break
                except:
                    continue
            
            if input_element:
                # 测试基本游戏命令
                test_commands = ["look", "help", "who"]
                
                for cmd in test_commands:
                    await input_element.fill(cmd)
                    await self.page.keyboard.press('Enter')
                    await self.page.wait_for_timeout(1000)
                
                self.log_result("交互功能", True, f"成功测试命令: {test_commands}")
                return True
            else:
                self.log_result("交互功能", False, "未找到命令输入框")
                return False
                
        except Exception as e:
            self.log_result("交互功能", False, f"测试失败: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🎭 开始AI导演系统Playwright完整功能测试")
        print(f"   测试目标: {self.base_url}")
        print(f"   开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
        
        # 初始化浏览器
        if not await self.setup():
            print("❌ 浏览器初始化失败，测试终止")
            return
        
        try:
            # 执行测试序列
            tests = [
                ("Evennia主页访问", self.test_homepage_access),
                ("Web客户端访问", self.test_webclient_access),
                ("AI导演API端点", self.test_api_endpoints),
                ("性能基准测试", self.test_performance_benchmark),
                ("交互功能测试", self.test_interactive_features)
            ]
            
            for test_name, test_func in tests:
                print(f"\n🧪 执行测试: {test_name}")
                await test_func()
                await self.page.wait_for_timeout(1000)  # 测试间隔
            
        finally:
            await self.teardown()
        
        # 输出测试总结
        self.print_summary()
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 70)
        print("🎭 AI导演系统Playwright测试总结")
        print("=" * 70)
        
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        
        for result in self.test_results:
            status = "✅ 通过" if result['success'] else "❌ 失败"
            print(f"{result['test']}: {status}")
            if result['message']:
                print(f"   {result['message']}")
        
        print(f"\n总计: {passed}/{total} 测试通过")
        
        if passed == total:
            print("\n🎉 所有测试通过!")
            print("✅ AI导演系统在Evennia Web环境中完全正常工作")
            print("✅ 全局配置管理正常")
            print("✅ API端点响应正常")
            print("✅ 性能表现优秀")
        elif passed >= total * 0.8:
            print(f"\n🎯 大部分测试通过! ({passed}/{total})")
            print("AI导演系统基本功能正常，有少量问题需要关注")
        else:
            print(f"\n⚠️ 有 {total - passed} 个测试失败")
            print("需要检查AI导演系统的集成问题")


async def main():
    """主函数"""
    tester = AIDirectorPlaywrightTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
