"""
简单测试页面 - 不依赖Handler系统
"""

from django.http import HttpResponse
from django.shortcuts import render

def simple_test_page(request):
    """简单测试页面"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>简单测试页面</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }
            .container {
                background: rgba(255, 255, 255, 0.1);
                padding: 30px;
                border-radius: 15px;
                backdrop-filter: blur(10px);
            }
            h1 {
                text-align: center;
                margin-bottom: 30px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .test-section {
                margin: 20px 0;
                padding: 20px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
            }
            button {
                background: linear-gradient(45deg, #ff6b6b, #ee5a24);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin: 5px;
                transition: transform 0.2s;
            }
            button:hover {
                transform: translateY(-2px);
            }
            .result {
                margin-top: 10px;
                padding: 10px;
                background: rgba(0, 0, 0, 0.2);
                border-radius: 5px;
                min-height: 50px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🧙‍♂️ 修仙MUD 简单测试页面</h1>
            
            <div class="test-section">
                <h3>基础功能测试</h3>
                <button onclick="testBasic()">测试基础功能</button>
                <button onclick="testTime()">获取当前时间</button>
                <button onclick="testRandom()">生成随机数</button>
                <div id="basic-result" class="result">点击按钮开始测试...</div>
            </div>
            
            <div class="test-section">
                <h3>系统信息</h3>
                <button onclick="getSystemInfo()">获取系统信息</button>
                <div id="system-result" class="result">点击按钮获取系统信息...</div>
            </div>
            
            <div class="test-section">
                <h3>测试状态</h3>
                <p>✅ 页面加载成功</p>
                <p>✅ JavaScript功能正常</p>
                <p>✅ CSS样式正常</p>
                <p id="test-status">🔄 等待测试...</p>
            </div>
        </div>
        
        <script>
            function testBasic() {
                const result = document.getElementById('basic-result');
                result.innerHTML = '✅ 基础功能测试通过！页面可以正常响应JavaScript事件。';
                updateTestStatus('基础功能测试完成');
            }
            
            function testTime() {
                const result = document.getElementById('basic-result');
                const now = new Date();
                result.innerHTML = `🕐 当前时间: ${now.toLocaleString('zh-CN')}`;
                updateTestStatus('时间获取测试完成');
            }
            
            function testRandom() {
                const result = document.getElementById('basic-result');
                const randomNum = Math.floor(Math.random() * 1000) + 1;
                result.innerHTML = `🎲 随机数: ${randomNum}`;
                updateTestStatus('随机数生成测试完成');
            }
            
            function getSystemInfo() {
                const result = document.getElementById('system-result');
                const info = {
                    userAgent: navigator.userAgent,
                    language: navigator.language,
                    platform: navigator.platform,
                    cookieEnabled: navigator.cookieEnabled,
                    onLine: navigator.onLine
                };
                
                let infoHtml = '<h4>浏览器信息:</h4>';
                for (const [key, value] of Object.entries(info)) {
                    infoHtml += `<p><strong>${key}:</strong> ${value}</p>`;
                }
                
                result.innerHTML = infoHtml;
                updateTestStatus('系统信息获取完成');
            }
            
            function updateTestStatus(message) {
                const status = document.getElementById('test-status');
                status.innerHTML = `✅ ${message} - ${new Date().toLocaleTimeString('zh-CN')}`;
            }
            
            // 页面加载完成后的初始化
            document.addEventListener('DOMContentLoaded', function() {
                updateTestStatus('页面加载完成，准备就绪');
            });
        </script>
    </body>
    </html>
    """
    
    return HttpResponse(html_content)
