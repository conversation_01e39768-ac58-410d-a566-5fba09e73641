#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Evennia模拟服务器
用于测试AI导演功能的Web界面
"""

import os
import sys
import json
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

class EvenniaMockHandler(BaseHTTPRequestHandler):
    """模拟Evennia Web服务器的处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/':
            self.serve_index_page()
        elif path == '/webclient/':
            self.serve_webclient_page()
        elif path.startswith('/api/'):
            self.serve_api_request()
        elif path.startswith('/static/'):
            self.serve_static_file(path)
        else:
            self.send_404()
    
    def do_POST(self):
        """处理POST请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path.startswith('/api/'):
            self.serve_api_request()
        else:
            self.send_404()
    
    def serve_index_page(self):
        """提供主页"""
        html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仙侠MUD - Evennia游戏</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #1a1a1a; color: #fff; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 40px; }
        .login-form { background: #2a2a2a; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; }
        input { width: 100%; padding: 10px; border: 1px solid #555; background: #333; color: #fff; border-radius: 5px; }
        button { background: #4CAF50; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #45a049; }
        .links { text-align: center; }
        .links a { color: #4CAF50; text-decoration: none; margin: 0 15px; }
        .links a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏮 仙侠修仙世界 🏮</h1>
            <p>基于Evennia的多人在线修仙游戏</p>
        </div>
        
        <div class="login-form">
            <h2>登录游戏</h2>
            <form id="loginForm" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit">进入游戏</button>
            </form>
        </div>
        
        <div class="links">
            <a href="/webclient/">Web客户端</a>
            <a href="/admin/">管理后台</a>
            <a href="/api/">API文档</a>
        </div>
    </div>
    
    <script>
        function handleLogin(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username && password) {
                // 模拟登录成功，跳转到webclient
                window.location.href = '/webclient/';
            } else {
                alert('请输入用户名和密码');
            }
        }
    </script>
</body>
</html>
        """
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_webclient_page(self):
        """提供Web客户端页面"""
        html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仙侠MUD - Web客户端</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background: #1a1a1a; color: #fff; }
        .game-container { display: flex; height: 100vh; }
        .main-area { flex: 1; display: flex; flex-direction: column; }
        .output-area { flex: 1; background: #2a2a2a; padding: 20px; overflow-y: auto; border: 1px solid #555; }
        .input-area { background: #333; padding: 15px; border-top: 1px solid #555; }
        .input-area input { width: 100%; padding: 10px; background: #444; color: #fff; border: 1px solid #666; border-radius: 5px; }
        .sidebar { width: 300px; background: #333; padding: 20px; border-left: 1px solid #555; }
        .ai-director-panel { background: #2a2a2a; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .ai-director-panel h3 { margin-top: 0; color: #4CAF50; }
        .test-button { background: #4CAF50; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #45a049; }
        .log-area { background: #1a1a1a; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto; font-size: 12px; }
        .message { margin-bottom: 10px; }
        .message.system { color: #4CAF50; }
        .message.ai-director { color: #FFD700; }
        .message.error { color: #FF6B6B; }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="main-area">
            <div class="output-area" id="gameOutput">
                <div class="message system">🎭 欢迎来到仙侠修仙世界！</div>
                <div class="message system">🌟 AI导演系统已激活，将为您提供智能剧情体验</div>
                <div class="message">你站在青云峰山脚下，远山如黛，云雾缭绕...</div>
            </div>
            <div class="input-area">
                <input type="text" id="commandInput" placeholder="输入游戏命令..." onkeypress="handleCommand(event)">
            </div>
        </div>
        
        <div class="sidebar">
            <div class="ai-director-panel">
                <h3>🎭 AI导演测试</h3>
                <button class="test-button" onclick="testStoryOutline()">测试故事解析</button>
                <button class="test-button" onclick="testAIDecision()">测试AI决策</button>
                <button class="test-button" onclick="testPerformance()">性能测试</button>
                <div class="log-area" id="testLog"></div>
            </div>
            
            <div class="ai-director-panel">
                <h3>📊 系统状态</h3>
                <div id="systemStatus">
                    <div>AI导演: <span style="color: #4CAF50;">运行中</span></div>
                    <div>响应时间: <span id="responseTime">--</span>ms</div>
                    <div>决策缓存: <span id="cacheStatus">--</span></div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let testLogElement = document.getElementById('testLog');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'message ' + type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            testLogElement.appendChild(logEntry);
            testLogElement.scrollTop = testLogElement.scrollHeight;
        }
        
        function handleCommand(event) {
            if (event.key === 'Enter') {
                const input = event.target.value.trim();
                if (input) {
                    addGameMessage(`> ${input}`, 'user');
                    processCommand(input);
                    event.target.value = '';
                }
            }
        }
        
        function addGameMessage(message, type = 'game') {
            const output = document.getElementById('gameOutput');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + type;
            messageDiv.textContent = message;
            output.appendChild(messageDiv);
            output.scrollTop = output.scrollHeight;
        }
        
        function processCommand(command) {
            // 模拟游戏响应
            setTimeout(() => {
                if (command.includes('修炼') || command.includes('突破')) {
                    addGameMessage('🌟 你开始修炼，天地灵气汇聚...', 'system');
                    triggerAIDirector('cultivation_breakthrough');
                } else if (command.includes('查看') || command.includes('look')) {
                    addGameMessage('你仔细观察周围环境...', 'game');
                } else {
                    addGameMessage('你执行了命令，但什么也没有发生。', 'game');
                }
            }, 500);
        }
        
        function triggerAIDirector(eventType) {
            setTimeout(() => {
                const aiMessages = [
                    '🎭 AI导演：天地灵气震荡，预示着重大事件即将发生...',
                    '🎭 AI导演：你的修炼引起了暗中观察者的注意。',
                    '🎭 AI导演：远处传来一声龙吟，似乎与你的突破有关。'
                ];
                const randomMessage = aiMessages[Math.floor(Math.random() * aiMessages.length)];
                addGameMessage(randomMessage, 'ai-director');
            }, 1000);
        }
        
        async function testStoryOutline() {
            log('开始测试故事大纲解析...', 'info');
            
            try {
                const response = await fetch('/api/ai-director/parse-outline/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        outline_text: '《逆天改命》主题：凡人逆天修仙，挑战命运束缚'
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    log(`✓ 解析成功: ${result.title}`, 'system');
                    log(`主题: ${result.theme}`, 'info');
                } else {
                    log(`✗ 解析失败: ${result.error}`, 'error');
                }
            } catch (error) {
                log(`✗ 请求失败: ${error.message}`, 'error');
            }
        }
        
        async function testAIDecision() {
            log('开始测试AI决策生成...', 'info');
            
            try {
                const response = await fetch('/api/ai-director/make-decision/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        event_type: 'CultivationBreakthrough',
                        character: 'testuser',
                        description: '玩家突破到筑基期'
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    log(`✓ 决策生成成功`, 'system');
                    log(`类型: ${result.decision_type}`, 'info');
                    log(`响应时间: ${result.response_time}ms`, 'info');
                    document.getElementById('responseTime').textContent = result.response_time;
                } else {
                    log(`✗ 决策失败: ${result.error}`, 'error');
                }
            } catch (error) {
                log(`✗ 请求失败: ${error.message}`, 'error');
            }
        }
        
        async function testPerformance() {
            log('开始性能测试...', 'info');
            
            const times = [];
            for (let i = 0; i < 5; i++) {
                try {
                    const start = Date.now();
                    const response = await fetch('/api/ai-director/make-decision/', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            event_type: 'TestEvent',
                            character: 'testuser',
                            test_id: i
                        })
                    });
                    const end = Date.now();
                    times.push(end - start);
                    log(`测试 ${i+1}: ${end - start}ms`, 'info');
                } catch (error) {
                    log(`测试 ${i+1} 失败: ${error.message}`, 'error');
                }
            }
            
            if (times.length > 0) {
                const avg = times.reduce((a, b) => a + b, 0) / times.length;
                log(`平均响应时间: ${avg.toFixed(1)}ms`, 'system');
            }
        }
        
        // 初始化
        log('AI导演测试面板已加载', 'system');
    </script>
</body>
</html>
        """
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_api_request(self):
        """处理API请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # 模拟AI导演API响应
        if path == '/api/ai-director/parse-outline/':
            response = {
                "success": True,
                "outline_id": f"outline_{int(time.time())}",
                "title": "逆天改命",
                "theme": "凡人逆天修仙，挑战命运束缚",
                "main_conflict": "废灵根与天道的对抗",
                "key_characters": ["林逸风", "苏清雪"],
                "plot_points": 3,
                "phases": ["序章", "起承", "高潮", "转合", "终章"]
            }
        elif path == '/api/ai-director/make-decision/':
            response = {
                "success": True,
                "decision_id": f"decision_{int(time.time())}",
                "decision_type": "剧情推进",
                "content": "天地灵气震荡，预示着重大事件即将发生。此子悟性非凡，未来可期。",
                "confidence": 0.85,
                "response_time": 45.2,
                "next_actions": ["调查灵气源头", "加强防御"]
            }
        else:
            response = {"success": False, "error": "API端点不存在"}
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def serve_static_file(self, path):
        """提供静态文件"""
        self.send_404()
    
    def send_404(self):
        """发送404响应"""
        self.send_response(404)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(b'<h1>404 Not Found</h1>')
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{time.strftime('%H:%M:%S')}] {format % args}")


def start_mock_server(port=8000):
    """启动模拟服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, EvenniaMockHandler)
    print(f"🎭 Evennia模拟服务器启动在 http://localhost:{port}")
    print("🌐 访问 http://localhost:8000 查看主页")
    print("🎮 访问 http://localhost:8000/webclient/ 进入游戏客户端")
    print("📡 API端点: /api/ai-director/")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()


if __name__ == "__main__":
    start_mock_server()
