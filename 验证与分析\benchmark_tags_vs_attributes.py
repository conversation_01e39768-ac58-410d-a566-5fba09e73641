#!/usr/bin/env python
"""
语义化高性能查询系统性能基准测试

验证TagProperty vs AttributeProperty的查询性能对比
测试大规模数据下的查询响应时间

使用方法：
1. 首先运行: evennia migrate
2. 然后运行: python benchmark_tags_vs_attributes.py
"""

import os
import sys
import time
import random
import statistics
from typing import List, Dict, Any

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "server.conf.settings")

# 初始化Evennia/Django
import django
django.setup()

from evennia.utils import create, search
from evennia.objects.models import ObjectDB
from evennia.typeclasses.tags import TagProperty
from evennia.typeclasses.attributes import AttributeProperty
from typeclasses.objects import Object
from typeclasses.characters import Character


class BenchmarkCharacter(Character):
    """基准测试用的角色类"""
    
    # TagProperty方式 - 语义化标签
    修为境界 = TagProperty(category="境界等级")
    门派 = TagProperty(category="门派归属") 
    职业 = TagProperty(category="职业类型")
    地区 = TagProperty(category="所在地区")
    等级段 = TagProperty(category="等级分组")
    
    # AttributeProperty方式 - 传统属性（用于对比）
    修为境界_attr = AttributeProperty(default="凡人")
    门派_attr = AttributeProperty(default="散修")
    职业_attr = AttributeProperty(default="平民")
    地区_attr = AttributeProperty(default="未知")
    等级段_attr = AttributeProperty(default="新手")


class PerformanceBenchmark:
    """性能基准测试类"""
    
    def __init__(self):
        self.境界列表 = ["炼气期", "筑基期", "金丹期", "元婴期", "化神期", "合体期", "大乘期", "渡劫期"]
        self.门派列表 = ["青云门", "天音寺", "万毒门", "焚香谷", "长生堂", "鬼王宗", "无极宫", "天策府"]
        self.职业列表 = ["剑修", "法修", "体修", "丹修", "阵修", "符修", "器修", "魂修"]
        self.地区列表 = ["东域", "西域", "南域", "北域", "中州", "蛮荒", "海外", "秘境"]
        self.等级列表 = ["新手", "初级", "中级", "高级", "专家", "大师", "宗师", "传说"]
        
        self.测试角色列表 = []
        self.性能报告 = {}
    
    def 清理数据(self):
        """清理测试数据"""
        print("正在清理测试数据...")
        
        # 删除所有BenchmarkCharacter实例
        for char in BenchmarkCharacter.objects.all():
            char.delete()
        
        # 清理相关标签
        from evennia.typeclasses.models import Tag
        Tag.objects.filter(db_model='objectdb', db_category__in=[
            "境界等级", "门派归属", "职业类型", "所在地区", "等级分组"
        ]).delete()
        
        print("数据清理完成")
    
    def 创建测试数据(self, 数量: int = 1000):
        """创建测试角色数据"""
        print(f"正在创建 {数量} 个测试角色...")
        
        开始时间 = time.time()
        
        for i in range(数量):
            # 随机选择属性值
            境界 = random.choice(self.境界列表)
            门派 = random.choice(self.门派列表)
            职业 = random.choice(self.职业列表)
            地区 = random.choice(self.地区列表)
            等级 = random.choice(self.等级列表)
            
            # 创建角色
            角色名 = f"测试角色_{i:04d}"
            
            try:
                # 使用create.create_object创建对象
                角色 = create.create_object(
                    BenchmarkCharacter,
                    key=角色名,
                    location=None
                )
                
                # 设置TagProperty（语义化标签）
                角色.tags.add(境界, category="境界等级")
                角色.tags.add(门派, category="门派归属")
                角色.tags.add(职业, category="职业类型")
                角色.tags.add(地区, category="所在地区")
                角色.tags.add(等级, category="等级分组")
                
                # 设置AttributeProperty（传统属性）
                角色.attributes.add("修为境界_attr", 境界)
                角色.attributes.add("门派_attr", 门派)
                角色.attributes.add("职业_attr", 职业)
                角色.attributes.add("地区_attr", 地区)
                角色.attributes.add("等级段_attr", 等级)
                
                self.测试角色列表.append(角色)
                
                if (i + 1) % 100 == 0:
                    print(f"已创建 {i + 1} 个角色...")
                    
            except Exception as e:
                print(f"创建角色 {角色名} 时出错: {e}")
                continue
        
        创建时间 = time.time() - 开始时间
        print(f"创建 {len(self.测试角色列表)} 个测试角色完成，耗时: {创建时间:.2f}秒")
        
        return len(self.测试角色列表)
    
    def 基准测试_标签查询(self, 测试次数: int = 100) -> Dict[str, float]:
        """测试TagProperty查询性能"""
        print(f"开始TagProperty查询性能测试 (测试次数: {测试次数})...")
        
        测试结果 = {
            "单条件查询": [],
            "多条件查询": [],
            "复合条件查询": [],
            "范围查询": []
        }
        
        for _ in range(测试次数):
            # 1. 单条件查询 - 查找筑基期角色
            开始时间 = time.time()
            result = search.search_object_by_tag(key="筑基期", category="境界等级")
            查询时间 = time.time() - 开始时间
            测试结果["单条件查询"].append(查询时间)
            
            # 2. 多条件查询 - 查找青云门的剑修
            开始时间 = time.time()
            # 先查青云门
            青云门角色 = search.search_object_by_tag(key="青云门", category="门派归属")
            # 再过滤剑修
            result = [char for char in 青云门角色 
                     if char.tags.get("剑修", category="职业类型")]
            查询时间 = time.time() - 开始时间
            测试结果["多条件查询"].append(查询时间)
            
            # 3. 复合条件查询 - 查找东域的金丹期以上修士
            开始时间 = time.time()
            东域角色 = search.search_object_by_tag(key="东域", category="所在地区")
            高境界角色 = []
            for char in 东域角色:
                境界 = char.tags.get("境界", category="境界等级", return_list=True)
                if 境界 and 境界[0] in ["金丹期", "元婴期", "化神期", "合体期", "大乘期", "渡劫期"]:
                    高境界角色.append(char)
            查询时间 = time.time() - 开始时间
            测试结果["复合条件查询"].append(查询时间)
            
            # 4. 范围查询 - 查找所有高级以上等级的角色
            开始时间 = time.time()
            高等级角色 = []
            for 等级 in ["高级", "专家", "大师", "宗师", "传说"]:
                result = search.search_object_by_tag(key=等级, category="等级分组")
                高等级角色.extend(result)
            查询时间 = time.time() - 开始时间
            测试结果["范围查询"].append(查询时间)
        
        # 计算平均值
        平均结果 = {}
        for 查询类型, 时间列表 in 测试结果.items():
            平均结果[查询类型] = {
                "平均时间": statistics.mean(时间列表),
                "最小时间": min(时间列表),
                "最大时间": max(时间列表),
                "标准差": statistics.stdev(时间列表) if len(时间列表) > 1 else 0
            }
        
        return 平均结果
    
    def 基准测试_属性查询(self, 测试次数: int = 100) -> Dict[str, float]:
        """测试AttributeProperty查询性能"""
        print(f"开始AttributeProperty查询性能测试 (测试次数: {测试次数})...")
        
        测试结果 = {
            "单条件查询": [],
            "多条件查询": [], 
            "复合条件查询": [],
            "范围查询": []
        }
        
        for _ in range(测试次数):
            # 1. 单条件查询 - 查找筑基期角色
            开始时间 = time.time()
            result = ObjectDB.objects.filter(
                db_attributes__db_key="修为境界_attr",
                db_attributes__db_value="筑基期"
            )
            list(result)  # 强制执行查询
            查询时间 = time.time() - 开始时间
            测试结果["单条件查询"].append(查询时间)
            
            # 2. 多条件查询 - 查找青云门的剑修
            开始时间 = time.time()
            result = ObjectDB.objects.filter(
                db_attributes__db_key="门派_attr",
                db_attributes__db_value="青云门"
            ).filter(
                db_attributes__db_key="职业_attr", 
                db_attributes__db_value="剑修"
            )
            list(result)  # 强制执行查询
            查询时间 = time.time() - 开始时间
            测试结果["多条件查询"].append(查询时间)
            
            # 3. 复合条件查询 - 查找东域的金丹期以上修士
            开始时间 = time.time()
            result = ObjectDB.objects.filter(
                db_attributes__db_key="地区_attr",
                db_attributes__db_value="东域"
            ).filter(
                db_attributes__db_key="修为境界_attr",
                db_attributes__db_value__in=["金丹期", "元婴期", "化神期", "合体期", "大乘期", "渡劫期"]
            )
            list(result)  # 强制执行查询
            查询时间 = time.time() - 开始时间
            测试结果["复合条件查询"].append(查询时间)
            
            # 4. 范围查询 - 查找所有高级以上等级的角色
            开始时间 = time.time()
            result = ObjectDB.objects.filter(
                db_attributes__db_key="等级段_attr",
                db_attributes__db_value__in=["高级", "专家", "大师", "宗师", "传说"]
            )
            list(result)  # 强制执行查询
            查询时间 = time.time() - 开始时间
            测试结果["范围查询"].append(查询时间)
        
        # 计算平均值
        平均结果 = {}
        for 查询类型, 时间列表 in 测试结果.items():
            平均结果[查询类型] = {
                "平均时间": statistics.mean(时间列表),
                "最小时间": min(时间列表),
                "最大时间": max(时间列表),
                "标准差": statistics.stdev(时间列表) if len(时间列表) > 1 else 0
            }
        
        return 平均结果
    
    def 数据库索引分析(self):
        """分析数据库索引情况"""
        from django.db import connection
        
        print("\n=== 数据库索引分析 ===")
        
        cursor = connection.cursor()
        
        # 分析Tags表索引
        print("\n1. Tags表索引:")
        cursor.execute("SHOW INDEX FROM typeclasses_tag")
        for index in cursor.fetchall():
            print(f"   {index}")
        
        # 分析Attributes表索引
        print("\n2. Attributes表索引:")
        cursor.execute("SHOW INDEX FROM typeclasses_attribute")
        for index in cursor.fetchall():
            print(f"   {index}")
            
        # 分析Objects表索引
        print("\n3. Objects表索引:")
        cursor.execute("SHOW INDEX FROM objects_objectdb")
        for index in cursor.fetchall():
            print(f"   {index}")
    
    def 生成性能报告(self, 标签结果: Dict, 属性结果: Dict):
        """生成详细的性能对比报告"""
        print("\n" + "="*80)
        print("语义化高性能查询系统 - 性能基准测试报告")
        print("="*80)
        
        print(f"\n测试数据量: {len(self.测试角色列表)} 个角色")
        print(f"测试环境: Evennia + Django ORM")
        print(f"数据库: SQLite/MySQL")
        
        print("\n" + "-"*60)
        print("性能对比结果 (单位: 毫秒)")
        print("-"*60)
        
        性能提升倍数汇总 = []
        
        for 查询类型 in 标签结果.keys():
            标签时间 = 标签结果[查询类型]["平均时间"] * 1000  # 转换为毫秒
            属性时间 = 属性结果[查询类型]["平均时间"] * 1000  # 转换为毫秒
            
            提升倍数 = 属性时间 / 标签时间 if 标签时间 > 0 else 0
            性能提升倍数汇总.append(提升倍数)
            
            print(f"\n{查询类型}:")
            print(f"  TagProperty查询:     {标签时间:.3f}ms")
            print(f"  AttributeProperty查询: {属性时间:.3f}ms")
            print(f"  性能提升倍数:         {提升倍数:.1f}x")
            print(f"  减少查询时间:         {属性时间-标签时间:.3f}ms ({((属性时间-标签时间)/属性时间*100):.1f}%)")
        
        平均提升倍数 = statistics.mean(性能提升倍数汇总)
        
        print("\n" + "-"*60)
        print("总体性能评估")
        print("-"*60)
        print(f"平均性能提升倍数: {平均提升倍数:.1f}x")
        print(f"最高性能提升倍数: {max(性能提升倍数汇总):.1f}x")
        print(f"最低性能提升倍数: {min(性能提升倍数汇总):.1f}x")
        
        # 理论分析
        print("\n" + "-"*60)
        print("性能优势原理分析")
        print("-"*60)
        print("1. 数据库索引优化:")
        print("   - Tags表: db_key, db_category, db_model, db_tagtype 复合索引")
        print("   - Attributes表: 仅 db_key 单列索引，db_value 无索引")
        
        print("\n2. 查询复杂度:")
        print("   - TagProperty: 直接JOIN预索引的Tags表")
        print("   - AttributeProperty: 需要序列化比较 db_value (PickledField)")
        
        print("\n3. 内存缓存:")
        print("   - TagProperty: 支持 TYPECLASS_AGGRESSIVE_CACHE")
        print("   - AttributeProperty: 每次都需要数据库查询")
        
        # 建议
        print("\n" + "-"*60)
        print("优化建议")
        print("-"*60)
        print("1. 对于频繁查询的分类数据，优先使用TagProperty")
        print("2. 对于复杂数据结构，仍使用AttributeProperty")
        print("3. 启用 TYPECLASS_AGGRESSIVE_CACHE = True")
        print("4. 合理设计Tag的category层次结构")
        
        # 验证理论
        if 平均提升倍数 >= 10:
            print(f"\n✅ 验证通过: TagProperty确实可以实现10+倍性能提升!")
        elif 平均提升倍数 >= 5:
            print(f"\n⚠️  部分验证: TagProperty实现了{平均提升倍数:.1f}倍性能提升")
        else:
            print(f"\n❌ 验证失败: 性能提升仅为{平均提升倍数:.1f}倍，未达到预期")
        
        return 平均提升倍数
    
    def 运行完整基准测试(self, 数据量: int = 1000, 测试次数: int = 50):
        """运行完整的基准测试"""
        print("开始语义化高性能查询系统基准测试...")
        print(f"测试参数: 数据量={数据量}, 测试次数={测试次数}")
        
        try:
            # 1. 清理和创建数据
            self.清理数据()
            实际数量 = self.创建测试数据(数据量)
            
            if 实际数量 < 100:
                print("警告: 测试数据量过少，可能影响性能测试准确性")
            
            # 2. 预热数据库连接
            print("预热数据库连接...")
            search.search_object_by_tag(key="筑基期", category="境界等级")
            ObjectDB.objects.filter(db_attributes__db_key="修为境界_attr").first()
            
            # 3. 执行性能测试
            标签结果 = self.基准测试_标签查询(测试次数)
            属性结果 = self.基准测试_属性查询(测试次数)
            
            # 4. 数据库索引分析
            # self.数据库索引分析()  # 可能在某些数据库中不可用
            
            # 5. 生成报告
            平均提升倍数 = self.生成性能报告(标签结果, 属性结果)
            
            # 6. 清理测试数据
            print("\n清理测试数据...")
            self.清理数据()
            
            return 平均提升倍数
            
        except Exception as e:
            print(f"基准测试过程中出现错误: {e}")
            # 确保清理数据
            try:
                self.清理数据()
            except:
                pass
            raise


if __name__ == "__main__":
    # 检查参数
    数据量 = 1000
    测试次数 = 50
    
    if len(sys.argv) > 1:
        数据量 = int(sys.argv[1])
    if len(sys.argv) > 2:
        测试次数 = int(sys.argv[2])
    
    # 运行基准测试
    benchmark = PerformanceBenchmark()
    
    try:
        性能提升倍数 = benchmark.运行完整基准测试(数据量, 测试次数)
        
        print(f"\n🎯 基准测试结论:")
        print(f"TagProperty相比AttributeProperty实现了 {性能提升倍数:.1f}倍 的查询性能提升")
        
        if 性能提升倍数 >= 10:
            print("✅ 完全验证了 '10-100倍性能提升' 的理论!")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n基准测试失败: {e}")
        import traceback
        traceback.print_exc()