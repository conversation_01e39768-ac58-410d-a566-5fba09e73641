#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智匠MindCraft AI客户端配置
基于OpenAI接口的AI服务调用
使用全局AI配置管理
"""

import os
import sys
import json
import time
from typing import List, Dict, Any, Optional

# 导入全局AI配置
try:
    # 尝试从项目根目录导入
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from ai_config import AIConfig, API_CONFIG, STORY_CONFIG
    print("使用全局AI配置")
except ImportError:
    # 如果无法导入全局配置，使用本地配置
    print("⚠️ 无法导入全局配置，使用本地配置")
    class AIConfig:
        MINDCRAFT_API_BASE = 'https://api.mindcraft.com.cn/v1'
        MINDCRAFT_API_KEY = 'MC-94D4CC750E92436FB3FA51C9F41D03A9'
        MINDCRAFT_MODEL = 'deepseek-r1-free'
        AI_USE_MOCK_CLIENT = False
        AI_AVAILABLE = True
        AI_SETTINGS = {
            'MAX_TOKENS': 4000,
            'TEMPERATURE': 0.7,
            'STREAM': False
        }
        @classmethod
        def get_api_config(cls):
            return {
                'api_base': cls.MINDCRAFT_API_BASE,
                'api_key': cls.MINDCRAFT_API_KEY,
                'model': cls.MINDCRAFT_MODEL,
                'timeout': 30,
                'max_retries': 3,
                **cls.AI_SETTINGS
            }
        @classmethod
        def is_available(cls):
            return cls.AI_AVAILABLE and bool(cls.MINDCRAFT_API_KEY)

    API_CONFIG = AIConfig.get_api_config()
    STORY_CONFIG = {'prompts': {}}

try:
    from openai import OpenAI
except ImportError:
    # 如果没有安装openai，使用模拟实现
    class OpenAI:
        def __init__(self, **kwargs):
            self.base_url = kwargs.get('base_url', '')
            self.api_key = kwargs.get('api_key', '')
            self.chat = self.Chat()

        class Chat:
            def __init__(self):
                self.completions = self.Completions()

            class Completions:
                def create(self, **kwargs):
                    import time
                    import random
                    time.sleep(0.05)  # 模拟网络延迟

                    class Choice:
                        def __init__(self):
                            self.message = self.Message()

                        class Message:
                            content = "天地灵气震荡，预示着重大事件即将发生。此子前途不可限量。"

                    class Response:
                        def __init__(self):
                            self.choices = [Choice()]

                    return Response()


class MindCraftAIClient:
    """智匠MindCraft AI客户端 - 使用全局配置"""

    def __init__(self):
        """初始化AI客户端"""
        # 使用全局配置
        self.config = API_CONFIG
        self.base_url = self.config['api_base']
        self.api_key = self.config['api_key']
        self.model = self.config['model']
        self.max_tokens = self.config['MAX_TOKENS']
        self.temperature = self.config['TEMPERATURE']
        self.stream = self.config['STREAM']
        self.timeout = self.config.get('timeout', 30)
        self.max_retries = self.config.get('max_retries', 3)

        if not self.api_key or self.api_key == "sk-mindcraft-api-key-here":
            raise ValueError("未配置有效的MINDCRAFT_API_KEY")

        # 初始化OpenAI客户端
        self.client = OpenAI(
            base_url=self.base_url,
            api_key=self.api_key,
            timeout=self.timeout
        )

        print(f"MindCraft AI客户端初始化成功")
        print(f"   模型: {self.model}")
        print(f"   API地址: {self.base_url}")
        print(f"   最大令牌: {self.max_tokens}")
        print(f"   温度参数: {self.temperature}")
        print(f"   超时时间: {self.timeout}s")
    
    def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        发送聊天完成请求
        
        Args:
            messages: 消息列表，格式: [{"role": "system/user/assistant", "content": "..."}]
            **kwargs: 额外参数
        
        Returns:
            AI生成的响应文本
        """
        try:
            # 合并参数
            params = {
                "model": self.model,
                "messages": messages,
                "temperature": kwargs.get('temperature', self.temperature),
                "max_tokens": kwargs.get('max_tokens', self.max_tokens),
                "stream": kwargs.get('stream', self.stream),
            }
            
            # 发送请求
            response = self.client.chat.completions.create(**params)
            
            if self.stream:
                # 处理流式响应
                result = ""
                for chunk in response:
                    if chunk.choices[0].delta.content:
                        result += chunk.choices[0].delta.content
                return result
            else:
                # 处理普通响应
                return response.choices[0].message.content
        
        except Exception as e:
            print(f"❌ AI请求失败: {e}")
            return f"AI服务暂时不可用: {str(e)}"
    
    def ai_director_decision(self, event_data: Dict[str, Any]) -> str:
        """
        AI导演决策生成 - 使用全局配置的提示词

        Args:
            event_data: 事件数据

        Returns:
            AI导演的决策和响应
        """
        # 使用全局配置的提示词
        system_prompt = STORY_CONFIG.get('prompts', {}).get(
            'decision_making',
            """你是一个仙侠MUD游戏的AI导演，负责根据游戏事件生成剧情响应和决策。

你的职责：
1. 分析游戏事件的影响和意义
2. 生成适合的剧情发展建议
3. 保持仙侠世界观的一致性
4. 让游戏体验更加有趣和引人入胜

回应格式要求：
- 简洁明了，符合仙侠风格
- 可以包含场景描述、NPC反应、环境变化等
- 保持神秘感和代入感"""
        )

        user_prompt = f"""游戏中发生了以下事件，请作为AI导演给出合适的剧情响应：

事件类型：{event_data.get('event_type', 'Unknown')}
事件内容：{event_data.get('description', '无描述')}
发生时间：{event_data.get('timestamp', '未知时间')}
相关角色：{event_data.get('character', '未知角色')}
事件重要性：{event_data.get('priority', 'NORMAL')}
位置信息：{event_data.get('location', '未知地点')}

请生成一个简短的剧情响应（50-200字），体现仙侠世界的氛围。"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        return self.chat_completion(messages, temperature=0.8, max_tokens=500)
    
    def agent_response(self, agent_type: str, context: str, character_name: str = "") -> str:
        """
        AI Agent响应生成
        
        Args:
            agent_type: Agent类型（tiandao/diling/qiling）
            context: 触发上下文
            character_name: 角色名称
        
        Returns:
            Agent的个性化响应
        """
        personalities = {
            "tiandao": "你是天道意识，代表宇宙意志和命运规律。说话神秘深邃，经常给出暗示性的预言或指引。",
            "diling": "你是地灵意识，代表场所的历史和记忆。说话亲切自然，善于讲述这个地方的故事传说。",
            "qiling": "你是器灵意识，代表法宝或物品的灵性。说话个性鲜明，有时骄傲有时调皮。"
        }
        
        system_prompt = f"""{personalities.get(agent_type, "你是一个神秘的存在")}

你需要根据当前情况给出20-80字的简短回应，要：
1. 符合你的身份和性格
2. 与当前情况相关
3. 保持仙侠风格
4. 不要过于直白，要有一定的神秘感"""

        user_prompt = f"""当前情况：{context}
角色：{character_name or '某位修行者'}

请以{agent_type}的身份给出一个简短回应："""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        return self.chat_completion(messages, temperature=0.9, max_tokens=200)
    
    def test_connection(self) -> bool:
        """测试AI连接"""
        try:
            test_messages = [
                {"role": "system", "content": "你是一个测试助手，请简单回应。"},
                {"role": "user", "content": "你好，请回复'AI连接正常'来确认服务可用。"}
            ]
            
            response = self.chat_completion(test_messages, max_tokens=50)
            
            if response and "AI连接正常" in response:
                print("AI连接测试成功")
                return True
            else:
                print(f"⚠️ AI连接测试部分成功，响应: {response}")
                return True  # 只要有响应就算成功
                
        except Exception as e:
            print(f"❌ AI连接测试失败: {e}")
            return False


class MockAIClient:
    """模拟AI客户端 - 用于开发和演示"""

    def __init__(self):
        """初始化模拟客户端"""
        print("模拟AI客户端初始化成功")
        print("   模式: 演示模式")
        print("   响应: 预设模拟响应")

    def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """模拟聊天完成"""
        import time
        import random

        # 模拟网络延迟
        time.sleep(random.uniform(0.02, 0.08))

        # 根据消息内容生成不同的模拟响应
        last_message = messages[-1]['content'].lower() if messages else ""

        if '故事' in last_message or '大纲' in last_message:
            responses = [
                "这是一个关于逆天改命的仙侠传奇，主角林逸风将在修仙路上遇到重重挑战。",
                "故事围绕废灵根修士的逆袭展开，充满了机遇与危险。",
                "天道无情，但人定胜天。这个故事将展现修仙者的不屈意志。"
            ]
        elif '突破' in last_message or '修炼' in last_message:
            responses = [
                "天地灵气震荡，预示着重大事件即将发生。此子悟性非凡，前途不可限量。",
                "灵气汇聚成漩涡，周围的花草树木都在这股力量下轻微摇摆，仿佛在为修行者的突破而庆贺。",
                "远山传来一声龙吟，似乎是对这次突破的回应。天道有感，必有异象。"
            ]
        elif '测试' in last_message or 'test' in last_message:
            return "AI连接正常，模拟客户端工作正常。"
        else:
            responses = [
                "天机不可泄露，一切自有定数。",
                "修仙路漫漫，需要坚定的道心。",
                "机缘巧合之下，或许会有意想不到的收获。",
                "此地灵气充沛，是修炼的好地方。",
                "前路虽然未卜，但只要心怀正道，必能化险为夷。"
            ]

        return random.choice(responses)

    def ai_director_decision(self, event_data: Dict[str, Any]) -> str:
        """模拟AI导演决策"""
        event_type = event_data.get('event_type', 'Unknown')
        character = event_data.get('character', '某位修行者')

        if 'breakthrough' in event_type.lower() or '突破' in str(event_data):
            return f"天地为之震动，{character}的突破引起了暗中观察者的注意。远处传来若有若无的龙吟声，似乎在为这次突破而庆贺。"
        elif 'meeting' in event_type.lower() or '相遇' in str(event_data):
            return f"缘分天定，{character}的这次相遇并非偶然。冥冥之中，似乎有一股力量在引导着事情的发展。"
        elif 'conflict' in event_type.lower() or '冲突' in str(event_data):
            return f"杀气弥漫，{character}面临的挑战远比表面看起来更加危险。暗流涌动，一场更大的风暴正在酝酿。"
        else:
            return f"天机莫测，{character}的每一个选择都可能改变命运的轨迹。当下的平静，或许正是暴风雨前的宁静。"

    def agent_response(self, agent_type: str, context: str, character_name: str = "") -> str:
        """模拟Agent响应"""
        responses = {
            "tiandao": [
                "天道轮回，因果循环，一切皆有定数。",
                "此子与天道有缘，未来成就不可限量。",
                "天机不可泄露，但可以给你一个提示..."
            ],
            "diling": [
                "这里曾经发生过一段传奇故事...",
                "此地灵气充沛，是修炼的绝佳之所。",
                "我见证了无数修行者的成长历程。"
            ],
            "qiling": [
                "哼，又是一个想要得到我认可的凡人。",
                "你的资质还算不错，勉强配得上我。",
                "想要发挥我的真正力量，还需要更多的修炼。"
            ]
        }

        import random
        return random.choice(responses.get(agent_type, ["神秘的力量在回应着你..."]))

    def test_connection(self) -> bool:
        """模拟连接测试"""
        print("模拟客户端连接测试成功")
        return True


# 全局AI客户端实例
_ai_client = None

def get_ai_client():
    """获取AI客户端单例 - 支持模拟模式"""
    global _ai_client

    if _ai_client is None:
        try:
            # 检查是否使用模拟客户端
            if AIConfig.AI_USE_MOCK_CLIENT:
                print("使用模拟AI客户端")
                _ai_client = MockAIClient()
            elif AIConfig.is_available():
                print("使用真实AI客户端")
                _ai_client = MindCraftAIClient()
            else:
                print("AI服务不可用，使用模拟客户端")
                _ai_client = MockAIClient()
        except Exception as e:
            print(f"AI客户端初始化失败，使用模拟客户端: {e}")
            _ai_client = MockAIClient()

    return _ai_client


if __name__ == "__main__":
    # 测试AI客户端
    print("🧪 测试MindCraft AI客户端...")
    
    client = get_ai_client()
    
    # 测试连接
    if client.test_connection():
        print("\n🎭 测试AI导演功能...")
        
        # 测试AI导演决策
        test_event = {
            "event_type": "CultivationBreakthroughEvent",
            "description": "玩家testuser成功突破到筑基期",
            "timestamp": "2025-06-29 14:30:00",
            "character": "testuser",
            "priority": "HIGH"
        }
        
        director_response = client.ai_director_decision(test_event)
        print(f"AI导演响应: {director_response}")
        
        print("\n🌟 测试AI Agent功能...")
        
        # 测试天道意识
        agent_response = client.agent_response(
            "tiandao", 
            "玩家在此地进行修炼，引起了天地灵气的波动", 
            "testuser"
        )
        print(f"天道意识: {agent_response}")
    else:
        print("❌ AI连接失败，请检查配置") 