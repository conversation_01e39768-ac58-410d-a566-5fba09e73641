secret_settings.py file not found or failed to import.
] Loading C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\server.py...
2025-06-30 11:59:04 [..] Loaded.
2025-06-30 11:59:04 [..] twistd 25.5.0 (C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\python.exe 3.13.3) starting up.
2025-06-30 11:59:04 [..] reactor class: twisted.internet.selectreactor.SelectReactor.
2025-06-30 11:59:04 [..] Webserver starting on 4005
2025-06-30 11:59:04 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 11:59:04 [..] Hand<PERSON> registered: cultivationhandler in category 'cultivation'
2025-06-30 11:59:04 [..] Hand<PERSON> registered: combatskillhandler in category 'combat'
2025-06-30 11:59:04 [..] Hand<PERSON> registered: alchemyhandler in category 'alchemy'
2025-06-30 11:59:04 [..] <PERSON><PERSON> registered: karmahandler in category 'karma'
2025-06-30 11:59:04 [..] <PERSON><PERSON> registered: aidirectorhandler in category 'ai_director'
2025-06-30 11:59:04 [..] TagProperty索引管理器已初始化
2025-06-30 11:59:04 [EE] 'TagHandler' object has no attribute 'set'
2025-06-30 11:59:04 [..] Traceback (most recent call last):
2025-06-30 11:59:04 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 101, in create_objects
2025-06-30 11:59:04 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 11:59:04 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\utils\idmapper\manager.py", line 33, in get
2025-06-30 11:59:04 [..]     inst = super().get(*args, **kwargs)
2025-06-30 11:59:04 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
2025-06-30 11:59:04 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 11:59:04 [..]            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
2025-06-30 11:59:04 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\django\db\models\query.py", line 637, in get
2025-06-30 11:59:04 [..]     raise self.model.DoesNotExist(
2025-06-30 11:59:04 [..]         "%s matching query does not exist." % self.model._meta.object_name
2025-06-30 11:59:04 [..]     )
2025-06-30 11:59:04 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 11:59:04 [..] During handling of the above exception, another exception occurred:
None2025-06-30 11:59:04 [..] Traceback (most recent call last):
2025-06-30 11:59:04 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\service.py", line 395, in run_initial_setup
2025-06-30 11:59:04 [..]     initial_setup.handle_setup()
2025-06-30 11:59:04 [..]     ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
2025-06-30 11:59:04 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 217, in handle_setup
2025-06-30 11:59:04 [..]     setup_sequence[stepname]()
2025-06-30 11:59:04 [..]     ~~~~~~~~~~~~~~~~~~~~~~~~^^
2025-06-30 11:59:04 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 107, in create_objects
2025-06-30 11:59:04 [..]     raise Exception(str(errors))
2025-06-30 11:59:04 [..] Exception: ["An error occurred while creating object 'admin object: 'TagHandler' object has no attribute 'set'"]
None2025-06-30 11:59:04 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 11:59:04 [..] Evennia Server successfully started.
2025-06-30 11:59:05 [..] Server disconnected from the portal.
2025-06-30 11:59:05 [..] Main loop terminated.
2025-06-30 11:59:05 [..] Server Shut Down.
2025-06-30 11:59:05 [..] Server Shut Down.
secret_settings.py file not found or failed to import.
] Loading C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\server.py...
2025-06-30 12:01:26 [..] Loaded.
2025-06-30 12:01:26 [..] twistd 25.5.0 (C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\python.exe 3.13.3) starting up.
2025-06-30 12:01:26 [..] reactor class: twisted.internet.selectreactor.SelectReactor.
2025-06-30 12:01:26 [..] Webserver starting on 4005
2025-06-30 12:01:26 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 12:01:26 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 12:01:26 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 12:01:26 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 12:01:26 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 12:01:26 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 12:01:26 [..] TagProperty索引管理器已初始化
2025-06-30 12:01:26 [EE] 'TagHandler' object has no attribute 'set'
2025-06-30 12:01:26 [..] Traceback (most recent call last):
2025-06-30 12:01:26 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 101, in create_objects
2025-06-30 12:01:26 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 12:01:26 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\utils\idmapper\manager.py", line 33, in get
2025-06-30 12:01:26 [..]     inst = super().get(*args, **kwargs)
2025-06-30 12:01:26 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
2025-06-30 12:01:26 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 12:01:26 [..]            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
2025-06-30 12:01:26 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\django\db\models\query.py", line 637, in get
2025-06-30 12:01:26 [..]     raise self.model.DoesNotExist(
2025-06-30 12:01:26 [..]         "%s matching query does not exist." % self.model._meta.object_name
2025-06-30 12:01:26 [..]     )
2025-06-30 12:01:26 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 12:01:26 [..] During handling of the above exception, another exception occurred:
None2025-06-30 12:01:26 [..] Traceback (most recent call last):
2025-06-30 12:01:26 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\service.py", line 395, in run_initial_setup
2025-06-30 12:01:26 [..]     initial_setup.handle_setup()
2025-06-30 12:01:26 [..]     ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
2025-06-30 12:01:26 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 217, in handle_setup
2025-06-30 12:01:26 [..]     setup_sequence[stepname]()
2025-06-30 12:01:26 [..]     ~~~~~~~~~~~~~~~~~~~~~~~~^^
2025-06-30 12:01:26 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 107, in create_objects
2025-06-30 12:01:26 [..]     raise Exception(str(errors))
2025-06-30 12:01:26 [..] Exception: ["An error occurred while creating object 'admin object: 'TagHandler' object has no attribute 'set'"]
None2025-06-30 12:01:26 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 12:01:26 [..] Evennia Server successfully started.
2025-06-30 12:01:27 [..] Server disconnected from the portal.
2025-06-30 12:01:27 [..] Main loop terminated.
2025-06-30 12:01:27 [..] Server Shut Down.
2025-06-30 12:01:27 [..] Server Shut Down.
secret_settings.py file not found or failed to import.
] Loading C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\server.py...
2025-06-30 12:06:47 [..] Loaded.
2025-06-30 12:06:47 [..] twistd 25.5.0 (C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\python.exe 3.13.3) starting up.
2025-06-30 12:06:47 [..] reactor class: twisted.internet.selectreactor.SelectReactor.
2025-06-30 12:06:47 [..] Webserver starting on 4005
2025-06-30 12:06:47 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 12:06:47 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 12:06:47 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 12:06:47 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 12:06:47 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 12:06:47 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 12:06:47 [..] TagProperty索引管理器已初始化
2025-06-30 12:06:47 [EE] 'TagHandler' object has no attribute 'set'
2025-06-30 12:06:47 [..] Traceback (most recent call last):
2025-06-30 12:06:47 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 101, in create_objects
2025-06-30 12:06:47 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 12:06:47 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\utils\idmapper\manager.py", line 33, in get
2025-06-30 12:06:47 [..]     inst = super().get(*args, **kwargs)
2025-06-30 12:06:47 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
2025-06-30 12:06:47 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 12:06:47 [..]            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
2025-06-30 12:06:47 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\django\db\models\query.py", line 637, in get
2025-06-30 12:06:47 [..]     raise self.model.DoesNotExist(
2025-06-30 12:06:47 [..]         "%s matching query does not exist." % self.model._meta.object_name
2025-06-30 12:06:47 [..]     )
2025-06-30 12:06:47 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 12:06:47 [..] During handling of the above exception, another exception occurred:
None2025-06-30 12:06:47 [..] Traceback (most recent call last):
2025-06-30 12:06:47 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\service.py", line 395, in run_initial_setup
2025-06-30 12:06:47 [..]     initial_setup.handle_setup()
2025-06-30 12:06:47 [..]     ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
2025-06-30 12:06:47 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 217, in handle_setup
2025-06-30 12:06:47 [..]     setup_sequence[stepname]()
2025-06-30 12:06:47 [..]     ~~~~~~~~~~~~~~~~~~~~~~~~^^
2025-06-30 12:06:47 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 107, in create_objects
2025-06-30 12:06:47 [..]     raise Exception(str(errors))
2025-06-30 12:06:47 [..] Exception: ["An error occurred while creating object 'admin object: 'TagHandler' object has no attribute 'set'"]
None2025-06-30 12:06:47 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 12:06:47 [..] Evennia Server successfully started.
2025-06-30 12:06:48 [..] Server disconnected from the portal.
2025-06-30 12:06:48 [..] Main loop terminated.
2025-06-30 12:06:48 [..] Server Shut Down.
2025-06-30 12:06:48 [..] Server Shut Down.
secret_settings.py file not found or failed to import.
] Loading C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\server.py...
2025-06-30 12:07:18 [..] Loaded.
2025-06-30 12:07:18 [..] twistd 25.5.0 (C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\python.exe 3.13.3) starting up.
2025-06-30 12:07:18 [..] reactor class: twisted.internet.selectreactor.SelectReactor.
2025-06-30 12:07:18 [..] Webserver starting on 4005
2025-06-30 12:07:18 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 12:07:18 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 12:07:18 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 12:07:18 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 12:07:18 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 12:07:18 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 12:07:18 [..] TagProperty索引管理器已初始化
2025-06-30 12:07:18 [EE] 'TagHandler' object has no attribute 'set'
2025-06-30 12:07:18 [..] Traceback (most recent call last):
2025-06-30 12:07:18 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 101, in create_objects
2025-06-30 12:07:18 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 12:07:18 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\utils\idmapper\manager.py", line 33, in get
2025-06-30 12:07:18 [..]     inst = super().get(*args, **kwargs)
2025-06-30 12:07:18 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
2025-06-30 12:07:18 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 12:07:18 [..]            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
2025-06-30 12:07:18 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\django\db\models\query.py", line 637, in get
2025-06-30 12:07:18 [..]     raise self.model.DoesNotExist(
2025-06-30 12:07:18 [..]         "%s matching query does not exist." % self.model._meta.object_name
2025-06-30 12:07:18 [..]     )
2025-06-30 12:07:18 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 12:07:18 [..] During handling of the above exception, another exception occurred:
None2025-06-30 12:07:18 [..] Traceback (most recent call last):
2025-06-30 12:07:18 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\service.py", line 395, in run_initial_setup
2025-06-30 12:07:18 [..]     initial_setup.handle_setup()
2025-06-30 12:07:18 [..]     ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
2025-06-30 12:07:18 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 217, in handle_setup
2025-06-30 12:07:18 [..]     setup_sequence[stepname]()
2025-06-30 12:07:18 [..]     ~~~~~~~~~~~~~~~~~~~~~~~~^^
2025-06-30 12:07:18 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 107, in create_objects
2025-06-30 12:07:18 [..]     raise Exception(str(errors))
2025-06-30 12:07:18 [..] Exception: ["An error occurred while creating object 'admin object: 'TagHandler' object has no attribute 'set'"]
None2025-06-30 12:07:18 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 12:07:18 [..] Evennia Server successfully started.
2025-06-30 12:07:19 [..] Server disconnected from the portal.
2025-06-30 12:07:19 [..] Main loop terminated.
2025-06-30 12:07:19 [..] Server Shut Down.
2025-06-30 12:07:19 [..] Server Shut Down.
secret_settings.py file not found or failed to import.
] Loading C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\server.py...
2025-06-30 12:19:29 [..] Loaded.
2025-06-30 12:19:29 [..] twistd 25.5.0 (C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\python.exe 3.13.3) starting up.
2025-06-30 12:19:29 [..] reactor class: twisted.internet.selectreactor.SelectReactor.
2025-06-30 12:19:29 [..] Webserver starting on 4005
2025-06-30 12:19:30 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 12:19:30 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 12:19:30 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 12:19:30 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 12:19:30 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 12:19:30 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 12:19:30 [..] TagProperty索引管理器已初始化
2025-06-30 12:19:30 [EE] 'TagHandler' object has no attribute 'set'
2025-06-30 12:19:30 [..] Traceback (most recent call last):
2025-06-30 12:19:30 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 101, in create_objects
2025-06-30 12:19:30 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 12:19:30 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\utils\idmapper\manager.py", line 33, in get
2025-06-30 12:19:30 [..]     inst = super().get(*args, **kwargs)
2025-06-30 12:19:30 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
2025-06-30 12:19:30 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 12:19:30 [..]            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
2025-06-30 12:19:30 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\django\db\models\query.py", line 637, in get
2025-06-30 12:19:30 [..]     raise self.model.DoesNotExist(
2025-06-30 12:19:30 [..]         "%s matching query does not exist." % self.model._meta.object_name
2025-06-30 12:19:30 [..]     )
2025-06-30 12:19:30 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 12:19:30 [..] During handling of the above exception, another exception occurred:
None2025-06-30 12:19:30 [..] Traceback (most recent call last):
2025-06-30 12:19:30 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\service.py", line 395, in run_initial_setup
2025-06-30 12:19:30 [..]     initial_setup.handle_setup()
2025-06-30 12:19:30 [..]     ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
2025-06-30 12:19:30 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 217, in handle_setup
2025-06-30 12:19:30 [..]     setup_sequence[stepname]()
2025-06-30 12:19:30 [..]     ~~~~~~~~~~~~~~~~~~~~~~~~^^
2025-06-30 12:19:30 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 107, in create_objects
2025-06-30 12:19:30 [..]     raise Exception(str(errors))
2025-06-30 12:19:30 [..] Exception: ["An error occurred while creating object 'admin object: 'TagHandler' object has no attribute 'set'"]
None2025-06-30 12:19:30 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 12:19:30 [..] Evennia Server successfully started.
2025-06-30 12:19:31 [..] Server disconnected from the portal.
2025-06-30 12:19:31 [..] Main loop terminated.
2025-06-30 12:19:31 [..] Server Shut Down.
2025-06-30 12:19:31 [..] Server Shut Down.
secret_settings.py file not found or failed to import.
] Loading C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\server.py...
2025-06-30 12:36:12 [..] Loaded.
2025-06-30 12:36:12 [..] twistd 25.5.0 (C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\python.exe 3.13.3) starting up.
2025-06-30 12:36:12 [..] reactor class: twisted.internet.selectreactor.SelectReactor.
2025-06-30 12:36:12 [..] Webserver starting on 4005
2025-06-30 12:36:12 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 12:36:12 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 12:36:12 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 12:36:12 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 12:36:12 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 12:36:12 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 12:36:12 [..] TagProperty索引管理器已初始化
2025-06-30 12:36:12 [EE] 'TagHandler' object has no attribute 'set'
2025-06-30 12:36:12 [..] Traceback (most recent call last):
2025-06-30 12:36:12 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 101, in create_objects
2025-06-30 12:36:12 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 12:36:12 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\utils\idmapper\manager.py", line 33, in get
2025-06-30 12:36:12 [..]     inst = super().get(*args, **kwargs)
2025-06-30 12:36:12 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
2025-06-30 12:36:12 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 12:36:12 [..]            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
2025-06-30 12:36:12 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\django\db\models\query.py", line 637, in get
2025-06-30 12:36:12 [..]     raise self.model.DoesNotExist(
2025-06-30 12:36:12 [..]         "%s matching query does not exist." % self.model._meta.object_name
2025-06-30 12:36:12 [..]     )
2025-06-30 12:36:12 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 12:36:12 [..] During handling of the above exception, another exception occurred:
None2025-06-30 12:36:12 [..] Traceback (most recent call last):
2025-06-30 12:36:12 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\service.py", line 395, in run_initial_setup
2025-06-30 12:36:12 [..]     initial_setup.handle_setup()
2025-06-30 12:36:12 [..]     ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
2025-06-30 12:36:12 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 217, in handle_setup
2025-06-30 12:36:12 [..]     setup_sequence[stepname]()
2025-06-30 12:36:12 [..]     ~~~~~~~~~~~~~~~~~~~~~~~~^^
2025-06-30 12:36:12 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.3\Lib\site-packages\evennia\server\initial_setup.py", line 107, in create_objects
2025-06-30 12:36:12 [..]     raise Exception(str(errors))
2025-06-30 12:36:12 [..] Exception: ["An error occurred while creating object 'admin object: 'TagHandler' object has no attribute 'set'"]
None2025-06-30 12:36:12 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 12:36:12 [..] Evennia Server successfully started.
2025-06-30 12:36:13 [..] Server disconnected from the portal.
2025-06-30 12:36:13 [..] Main loop terminated.
2025-06-30 12:36:13 [..] Server Shut Down.
2025-06-30 12:36:13 [..] Server Shut Down.
secret_settings.py file not found or failed to import.
] Loading C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\evennia\server\server.py...
2025-06-30 12:41:08 [..] Loaded.
2025-06-30 12:41:08 [..] twistd 23.10.0 (C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\python.exe 3.12.10) starting up.
2025-06-30 12:41:08 [..] reactor class: twisted.internet.selectreactor.SelectReactor.
2025-06-30 12:41:08 [..] Webserver starting on 4005
2025-06-30 12:41:08 [..] Initial setup: Creating objects (Account #1 and Limbo room) ...
2025-06-30 12:41:08 [..] Handler registered: cultivationhandler in category 'cultivation'
2025-06-30 12:41:08 [..] Handler registered: combatskillhandler in category 'combat'
2025-06-30 12:41:08 [..] Handler registered: alchemyhandler in category 'alchemy'
2025-06-30 12:41:08 [..] Handler registered: karmahandler in category 'karma'
2025-06-30 12:41:08 [..] Handler registered: aidirectorhandler in category 'ai_director'
2025-06-30 12:41:08 [..] TagProperty索引管理器已初始化
2025-06-30 12:41:08 [EE] 'TagHandler' object has no attribute 'set'
2025-06-30 12:41:08 [..] Traceback (most recent call last):
2025-06-30 12:41:08 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\evennia\server\initial_setup.py", line 101, in create_objects
2025-06-30 12:41:08 [..]     superuser_character = ObjectDB.objects.get(id=1)
2025-06-30 12:41:08 [..]                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 12:41:08 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\evennia\utils\idmapper\manager.py", line 33, in get
2025-06-30 12:41:08 [..]     inst = super().get(*args, **kwargs)
2025-06-30 12:41:08 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 12:41:08 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
2025-06-30 12:41:08 [..]     return getattr(self.get_queryset(), name)(*args, **kwargs)
2025-06-30 12:41:08 [..]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-30 12:41:08 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\django\db\models\query.py", line 637, in get
2025-06-30 12:41:08 [..]     raise self.model.DoesNotExist(
2025-06-30 12:41:08 [..] evennia.objects.models.ObjectDB.DoesNotExist: ObjectDB matching query does not exist.
None2025-06-30 12:41:08 [..] During handling of the above exception, another exception occurred:
None2025-06-30 12:41:08 [..] Traceback (most recent call last):
2025-06-30 12:41:08 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\evennia\server\service.py", line 395, in run_initial_setup
2025-06-30 12:41:08 [..]     initial_setup.handle_setup()
2025-06-30 12:41:08 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\evennia\server\initial_setup.py", line 217, in handle_setup
2025-06-30 12:41:08 [..]     setup_sequence[stepname]()
2025-06-30 12:41:08 [..]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\evennia\server\initial_setup.py", line 107, in create_objects
2025-06-30 12:41:08 [..]     raise Exception(str(errors))
2025-06-30 12:41:08 [..] Exception: ["An error occurred while creating object 'admin object: 'TagHandler' object has no attribute 'set'"]
None2025-06-30 12:41:08 [..] Error in initial setup. Stopping Server + Portal.
2025-06-30 12:41:08 [..] Evennia Server successfully started.
2025-06-30 12:41:09 [..] Server disconnected from the portal.
2025-06-30 12:41:09 [..] Main loop terminated.
2025-06-30 12:41:09 [..] Server Shut Down.
2025-06-30 12:41:09 [..] Server Shut Down.
