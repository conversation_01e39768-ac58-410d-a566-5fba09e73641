#!/usr/bin/env python
"""
简单的Web测试脚本 - 使用requests测试Handler生态组件化框架
"""

import requests
import json
import time


def test_web_api():
    """测试Web API功能"""
    base_url = "http://localhost:8000"
    
    print("🎮 Handler生态组件化框架 Web API测试")
    print("=" * 60)
    
    try:
        # 测试主页
        print("📡 测试主页...")
        response = requests.get(base_url)
        if response.status_code == 200:
            print("✓ 主页访问成功")
        else:
            print(f"❌ 主页访问失败: {response.status_code}")
            return False
        
        # 测试创建角色API
        print("\n🎭 测试创建角色API...")
        response = requests.get(f"{base_url}/api/create_character?name=API测试角色")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 创建角色: {data.get('message', 'Success')}")
            if data.get('success'):
                print(f"  角色ID: {data.get('character_id', 'N/A')}")
        else:
            print(f"❌ 创建角色失败: {response.status_code}")
        
        # 测试Handler功能API
        print("\n🔧 测试Handler功能API...")
        response = requests.get(f"{base_url}/api/test_handlers")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Handler测试: {data.get('message', 'Success')}")
            if data.get('details'):
                for detail in data['details']:
                    print(f"  - {detail}")
        else:
            print(f"❌ Handler测试失败: {response.status_code}")
        
        # 测试内存统计API
        print("\n📊 测试内存统计API...")
        response = requests.get(f"{base_url}/api/memory_stats")
        if response.status_code == 200:
            data = response.json()
            print("✓ 内存统计获取成功:")
            print(f"  - 当前Handler数量: {data.get('current_handler_count', 0)}")
            print(f"  - 总创建数量: {data.get('total_created', 0)}")
            print(f"  - 总清理数量: {data.get('total_cleaned', 0)}")
            print(f"  - 清理率: {data.get('cleanup_rate', '0%')}")
            print(f"  - 缓存命中率: {data.get('cache_hit_rate', '0%')}")
        else:
            print(f"❌ 内存统计失败: {response.status_code}")
        
        # 测试事件历史API
        print("\n📡 测试事件历史API...")
        response = requests.get(f"{base_url}/api/event_history")
        if response.status_code == 200:
            data = response.json()
            events = data.get('events', [])
            print(f"✓ 事件历史获取成功: 记录了 {len(events)} 个事件")
            for i, event in enumerate(events[:3]):  # 显示前3个事件
                print(f"  {i+1}. {event.get('event_type', 'Unknown')} - {event.get('source_handler', 'Unknown')}")
        else:
            print(f"❌ 事件历史失败: {response.status_code}")
        
        # 测试性能测试API
        print("\n⚡ 测试性能测试API...")
        response = requests.get(f"{base_url}/api/performance_test")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 性能测试: {data.get('message', 'Success')}")
            if data.get('performance'):
                perf = data['performance']
                print(f"  - 创建时间: {perf.get('creation_time', 'N/A')}")
                print(f"  - 访问时间: {perf.get('access_time', 'N/A')}")
                print(f"  - 创建角色数: {perf.get('characters_created', 0)}")
                print(f"  - 总访问次数: {perf.get('total_accesses', 0)}")
        else:
            print(f"❌ 性能测试失败: {response.status_code}")
        
        # 测试清理测试API
        print("\n🧹 测试清理测试API...")
        response = requests.get(f"{base_url}/api/cleanup_test")
        if response.status_code == 200:
            data = response.json()
            cleaned_count = data.get('cleaned_count', 0)
            print(f"✓ 清理测试: {data.get('message', 'Success')}")
            print(f"  清理了 {cleaned_count} 个Handler")
        else:
            print(f"❌ 清理测试失败: {response.status_code}")
        
        # 最终内存统计
        print("\n📊 最终内存统计...")
        response = requests.get(f"{base_url}/api/memory_stats")
        if response.status_code == 200:
            data = response.json()
            print("✓ 最终内存统计:")
            print(f"  - 当前内存使用: {data.get('current_memory_usage', 0)} 字节")
            print(f"  - 当前Handler数量: {data.get('current_handler_count', 0)}")
            print(f"  - 内存效率: {data.get('memory_efficiency', '0%')}")
            print(f"  - 总节省内存: {data.get('memory_saved', 0)} 字节")
        
        print("\n" + "=" * 60)
        print("🎉 Handler生态组件化框架 Web API测试完成！")
        print("✅ 所有API端点功能正常")
        print("✅ Handler系统在Web环境中运行正常")
        print("✅ 内存优化功能有效")
        print("✅ 事件通信机制正常")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到测试服务器，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False


def test_stress_performance():
    """压力测试性能"""
    base_url = "http://localhost:8000"
    
    print("\n🚀 Handler生态组件化框架压力测试")
    print("=" * 60)
    
    try:
        # 创建多个角色进行压力测试
        print("📈 创建多个角色进行压力测试...")
        start_time = time.time()
        
        for i in range(20):
            response = requests.get(f"{base_url}/api/create_character?name=压力测试角色{i}")
            if response.status_code != 200:
                print(f"❌ 创建角色{i}失败")
                break
        
        creation_time = time.time() - start_time
        print(f"✓ 创建20个角色耗时: {creation_time:.4f}秒")
        
        # 运行多次Handler测试
        print("🔧 运行多次Handler测试...")
        start_time = time.time()
        
        for i in range(10):
            response = requests.get(f"{base_url}/api/test_handlers")
            if response.status_code != 200:
                print(f"❌ Handler测试{i}失败")
                break
        
        handler_test_time = time.time() - start_time
        print(f"✓ 运行10次Handler测试耗时: {handler_test_time:.4f}秒")
        
        # 运行多次性能测试
        print("⚡ 运行多次性能测试...")
        start_time = time.time()
        
        for i in range(5):
            response = requests.get(f"{base_url}/api/performance_test")
            if response.status_code != 200:
                print(f"❌ 性能测试{i}失败")
                break
        
        perf_test_time = time.time() - start_time
        print(f"✓ 运行5次性能测试耗时: {perf_test_time:.4f}秒")
        
        # 获取最终统计
        response = requests.get(f"{base_url}/api/memory_stats")
        if response.status_code == 200:
            data = response.json()
            print("\n📊 压力测试后的内存统计:")
            print(f"  - 总创建Handler: {data.get('total_created', 0)}")
            print(f"  - 当前活跃Handler: {data.get('current_handler_count', 0)}")
            print(f"  - 缓存命中率: {data.get('cache_hit_rate', '0%')}")
        
        # 执行清理测试
        print("\n🧹 执行最终清理...")
        response = requests.get(f"{base_url}/api/cleanup_test")
        if response.status_code == 200:
            data = response.json()
            cleaned_count = data.get('cleaned_count', 0)
            print(f"✓ 最终清理了 {cleaned_count} 个Handler")
        
        print("\n" + "=" * 60)
        print("🎉 压力测试完成！")
        print(f"✅ 系统在高负载下运行稳定")
        print(f"✅ 平均创建时间: {creation_time/20:.4f}秒/角色")
        print(f"✅ 平均Handler测试时间: {handler_test_time/10:.4f}秒/次")
        
        return True
        
    except Exception as e:
        print(f"❌ 压力测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始Handler生态组件化框架Web测试...")
    
    # 基础API测试
    api_success = test_web_api()
    
    if api_success:
        # 压力测试
        stress_success = test_stress_performance()
        
        if stress_success:
            print("\n🏆 所有测试完成！Handler生态组件化框架Web功能验证通过！")
            return True
    
    print("\n❌ 测试未完全通过，请检查问题")
    return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
