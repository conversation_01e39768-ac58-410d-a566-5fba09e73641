#!/usr/bin/env python
"""
Handler生态组件化框架简单测试
"""

import time
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from systems.handler_system import (
        HandlerMemoryManager, HandlerCommunicationBus, HandlerDependencyManager,
        HandlerEventType, HandlerEvent, lazy_property, BaseHandler
    )
    print("✓ 成功导入Handler系统模块")
except ImportError as e:
    print(f"✗ 导入Handler系统模块失败: {e}")
    sys.exit(1)


class SimpleTestHandler(BaseHandler):
    """简单测试Handler"""
    
    def initialize(self):
        super().initialize()
        self.test_data = {"initialized": True, "value": 0}
        print(f"✓ {self.__class__.__name__} 初始化完成")
    
    def update_value(self, new_value):
        """更新测试值"""
        old_value = self.test_data.get("value", 0)
        self.test_data["value"] = new_value
        print(f"✓ 值更新: {old_value} -> {new_value}")


class SimpleTestCharacter:
    """简单测试角色"""
    
    def __init__(self, name="测试角色"):
        self.key = name
        self.id = f"test_char_{int(time.time() * 1000)}"
        self.name = name
    
    @lazy_property
    def test_handler(self):
        """测试Handler"""
        return SimpleTestHandler(self)
    
    def msg(self, message):
        print(f"[{self.name}] {message}")


def test_basic_functionality():
    """测试基础功能"""
    print("\n=== 测试基础功能 ===")
    
    try:
        # 创建测试角色
        char = SimpleTestCharacter("测试修仙者")
        print(f"✓ 创建角色: {char.name}")
        
        # 访问Handler
        handler = char.test_handler
        print(f"✓ 获取Handler: {handler}")
        
        # 测试Handler方法
        handler.update_value(100)
        value = handler.test_data["value"]
        print(f"✓ Handler方法调用成功，值为: {value}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基础功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_memory_manager():
    """测试内存管理器"""
    print("\n=== 测试内存管理器 ===")
    
    try:
        # 获取初始统计
        initial_report = HandlerMemoryManager.get_optimization_report()
        print(f"✓ 初始内存统计: {initial_report}")
        
        # 创建多个角色
        characters = []
        for i in range(5):
            char = SimpleTestCharacter(f"角色{i}")
            characters.append(char)
            # 访问Handler
            char.test_handler.update_value(i * 10)
        
        # 获取创建后统计
        after_report = HandlerMemoryManager.get_optimization_report()
        print(f"✓ 创建后统计: {after_report}")
        
        # 测试清理
        cleaned = HandlerMemoryManager.cleanup_inactive_handlers(max_idle_time=0)
        print(f"✓ 清理Handler数量: {cleaned}")
        
        return True
        
    except Exception as e:
        print(f"✗ 内存管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_event_system():
    """测试事件系统"""
    print("\n=== 测试事件系统 ===")
    
    try:
        # 创建事件
        event = HandlerEvent(
            HandlerEventType.HANDLER_CREATED,
            "SimpleTestHandler",
            data={"test": "data"}
        )
        print(f"✓ 创建事件: {event.event_id}")
        
        # 发布事件
        HandlerCommunicationBus.publish_event(event)
        print("✓ 事件发布成功")
        
        # 获取事件历史
        history = HandlerCommunicationBus.get_event_history()
        print(f"✓ 事件历史数量: {len(history)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 事件系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_lazy_property():
    """测试lazy_property性能"""
    print("\n=== 测试lazy_property性能 ===")
    
    try:
        char = SimpleTestCharacter("性能测试角色")
        
        # 测试首次访问
        start_time = time.time()
        handler1 = char.test_handler
        first_access_time = time.time() - start_time
        
        # 测试重复访问
        start_time = time.time()
        for _ in range(100):
            _ = char.test_handler
        repeated_access_time = (time.time() - start_time) / 100
        
        print(f"✓ 首次访问时间: {first_access_time:.6f}秒")
        print(f"✓ 重复访问平均时间: {repeated_access_time:.6f}秒")
        
        if repeated_access_time > 0:
            ratio = first_access_time / repeated_access_time
            print(f"✓ 性能提升比率: {ratio:.1f}x")
        
        return True
        
    except Exception as e:
        print(f"✗ lazy_property测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("Handler生态组件化框架简单测试开始")
    print("=" * 50)
    
    tests = [
        ("基础功能", test_basic_functionality),
        ("内存管理器", test_memory_manager),
        ("事件系统", test_event_system),
        ("lazy_property性能", test_lazy_property)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 Handler生态组件化框架基础测试全部通过！")
        print("✅ 基础功能 - 正常")
        print("✅ 内存管理 - 正常")
        print("✅ 事件系统 - 正常")
        print("✅ 性能优化 - 正常")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
