# 仙侠MUD UI设计方案总纲

## 📋 项目概述

### 设计目标
基于Evennia原生Web客户端架构，设计一个简洁实用的桌面端仙侠MUD界面，专注于10人左右的小规模部署，最大化利用Evennia现有功能，避免过度工程化。

### 核心理念
- **简洁优先**：避免复杂框架和过度优化
- **Evennia原生**：最大化利用Django模板和WebSocket
- **桌面专用**：专注1920x1080桌面端体验  
- **文字为王**：保持MUD核心的文字游戏特色
- **仙侠主题**：融入中国古典美学元素

## 🏗️ 技术架构

### 核心技术栈
```
前端技术栈：
• Django Templates (Evennia原生)
• 原生JavaScript ES6+ (无框架)
• CSS Grid + Flexbox (现代布局)
• WebSocket (Evennia内置)
• 仙侠主题CSS样式

后端集成：
• Evennia Command System
• Django Views & URLs
• WebSocket消息处理
• 简化的AI导演集成
• Attributes存储系统
```

### 文件结构
```
mygame/
├── web/
│   ├── templates/webclient/
│   │   ├── webclient.html          # 主界面模板
│   │   └── partials/               # 组件片段
│   │       ├── character_status.html    # 角色状态
│   │       ├── quick_actions.html       # 快捷操作
│   │       ├── ai_director_panel.html   # AI导演面板
│   │       ├── chat_channels.html       # 聊天频道
│   │       └── online_players.html      # 在线玩家
│   ├── static/webclient/
│   │   ├── css/
│   │   │   ├── xiuxian.css         # 仙侠主题样式
│   │   │   ├── layout.css          # 布局样式
│   │   │   └── ai_director.css     # AI导演样式
│   │   └── js/
│   │       ├── xiuxian_client.js   # 主客户端脚本
│   │       ├── ai_director.js      # AI导演功能
│   │       └── utils.js            # 工具函数
│   └── webclient/
│       └── views.py                # 自定义视图
├── commands/
│   └── ai_director_commands.py     # AI导演命令
└── typeclasses/
    └── ai_director.py              # AI导演类型类
```

## 🎨 界面设计

### 主界面布局 (1920x1080桌面端专用)

```
┌────────────────────────────────────────────────────────────────────────────┐
│ 🎭 仙侠MUD - AI导演驱动修仙世界 [状态：在线 | 境界：筑基三层 | 门派：青云门] │
├──────────────────┬───────────────────────────────────┬──────────────────────┤
│   左侧面板 (300px) │         主显示区域 (动态)           │   右侧面板 (320px)     │
│                  │                                   │                      │
│ ┌─ 角色状态 ────┐ │ ┌─ 游戏内容显示 ────────────────┐ │ ┌─ AI导演面板 ───┐ │
│ │ [头像] 李逍遥  │ │ │                             │ │ │ 状态：编剧中    │ │
│ │ 境界：筑基三层 │ │ │  🎭 你看到青云门大殿...        │ │ │ 剧情：筑基试炼  │ │
│ │ 生命：80/100  │ │ │  📜 师父说道："修仙之路..."    │ │ │ 进度：65%      │ │
│ │ 灵力：45/60   │ │ │  💫 AI导演：感受到灵气波动     │ │ │ 预告：即将遇到  │ │
│ │ 经验：1200    │ │ │                             │ │ │      神秘高人   │ │
│ └──────────────┘ │ │  > look                     │ │ └──────────────┘ │
│                  │ │  💡 建议：[修炼] [探索] [对话]  │ │                      │
│ ┌─ 快捷操作 ────┐ │ └─────────────────────────────┘ │ ┌─ 聊天频道 ────┐ │
│ │ [修炼] [背包] │ │                                   │ │ 世界：欢迎...   │ │
│ │ [技能] [地图] │ │ ┌─ 命令输入区 ──────────────────┐ │ │ 门派：师兄说... │ │
│ │ [任务] [门派] │ │ │ > _______________  [发送]     │ │ │ 私聊：无        │ │
│ │ [炼丹] [法宝] │ │ │ [↑历史] [Tab补全] [宏命令]    │ │ └──────────────┘ │
│ └──────────────┘ │ └─────────────────────────────┘ │                      │
│                  │                                   │ ┌─ 在线玩家 ────┐ │
│ ┌─ 地图概览 ────┐ │                                   │ │ 1. 张三丰 (元婴) │ │
│ │    [山]       │ │                                   │ │ 2. 小龙女 (金丹) │ │
│ │ [门]-[你]-[殿] │ │                                   │ │ 3. 杨过 (筑基)  │ │
│ │    [林]       │ │                                   │ │    ...         │ │
│ └──────────────┘ │                                   │ └──────────────┘ │
└──────────────────┴───────────────────────────────────┴──────────────────────┘
```

### 仙侠主题视觉设计

#### 色彩方案
```css
:root {
  /* 主色调 - 青云系 */
  --primary-color: #4A90E2;      /* 青云蓝 */
  --primary-light: #7FB3D3;      /* 浅青色 */
  --primary-dark: #2C5282;       /* 深青色 */
  
  /* 辅助色 - 古典系 */
  --gold-color: #D4A574;         /* 古典金 */
  --wood-color: #8B7355;         /* 古木色 */
  --jade-color: #7FB069;         /* 青玉色 */
  
  /* 功能色 */
  --text-primary: #2D3748;       /* 主文字 */
  --text-secondary: #4A5568;     /* 次要文字 */
  --background: #F7FAFC;         /* 背景色 */
  --panel-bg: rgba(255,255,255,0.95); /* 面板背景 */
  
  /* 品质颜色 */
  --quality-common: #9CA3AF;     /* 普通-灰 */
  --quality-uncommon: #10B981;   /* 优秀-绿 */
  --quality-rare: #3B82F6;       /* 稀有-蓝 */
  --quality-epic: #8B5CF6;       /* 史诗-紫 */
  --quality-legendary: #F59E0B;  /* 传说-金 */
  --quality-mythic: #EF4444;     /* 神话-红 */
}
```

#### 字体系统
```css
/* 中文字体栈 */
.font-chinese {
  font-family: "思源黑体", "Source Han Sans CN", "Microsoft YaHei", 
               "微软雅黑", "Helvetica Neue", Arial, sans-serif;
}

/* 古典标题字体 */
.font-classical {
  font-family: "楷体", KaiTi, "STKaiti", serif;
}

/* 等宽字体（命令行） */
.font-mono {
  font-family: "JetBrains Mono", "Fira Code", Consolas, 
               "Liberation Mono", Menlo, Courier, monospace;
}
```

## 🔧 核心功能实现

### 1. 简化的AI导演集成

#### Django命令实现
```python
# commands/ai_director_commands.py
from evennia import Command

class CmdAIStatus(Command):
    """查看AI导演状态"""
    key = "aistatus"
    aliases = ["ai"]
    
    def func(self):
        character = self.caller
        ai_status = character.ndb.ai_director_status or {
            'status': 'idle', 'chapter': '序章', 'progress': 0
        }
        
        self.caller.msg(f"""
🎭 AI导演状态报告
━━━━━━━━━━━━━━━━━━
状态: {ai_status['status']}
当前章节: {ai_status['chapter']}
进度: {ai_status['progress']}%
━━━━━━━━━━━━━━━━━━
        """)

class CmdXiuLian(Command):
    """修炼命令"""
    key = "xiulian"
    aliases = ["修炼", "cultivate"]
    
    def func(self):
        character = self.caller
        current_exp = character.db.cultivation_exp or 0
        exp_gain = 10
        
        character.db.cultivation_exp = current_exp + exp_gain
        self.caller.msg(f"🧘‍♂️ 你开始修炼，获得了 {exp_gain} 点修炼经验。")
        
        # 简单的AI导演响应
        if current_exp % 100 == 0:
            ai_message = "🎭 AI导演：感受到你修炼的进步，或许是时候寻找突破的契机了..."
            self.caller.msg(ai_message)
```

### 2. 前端JavaScript实现

#### 主客户端 (xiuxian_client.js)
```javascript
class XiuxianMUDClient {
  constructor() {
    this.websocket = null;
    this.connected = false;
    this.commandHistory = [];
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.connectWebSocket();
  }

  connectWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/`;
    
    this.websocket = new WebSocket(wsUrl);
    
    this.websocket.onopen = () => {
      this.connected = true;
      this.addToOutput('🎭 连接到仙侠修仙世界成功！', 'system');
    };
    
    this.websocket.onmessage = (event) => {
      this.handleMessage(JSON.parse(event.data));
    };
  }

  sendCommand() {
    const command = document.getElementById('command-input').value.trim();
    if (!command || !this.connected) return;
    
    this.websocket.send(JSON.stringify({
      type: 'command',
      text: command
    }));
    
    this.addToOutput(`> ${command}`, 'command');
    document.getElementById('command-input').value = '';
  }

  addToOutput(text, className = 'game') {
    const gameOutput = document.getElementById('game-output');
    const messageDiv = document.createElement('div');
    messageDiv.className = `output-line ${className}`;
    messageDiv.textContent = text;
    
    gameOutput.appendChild(messageDiv);
    gameOutput.scrollTop = gameOutput.scrollHeight;
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  window.xiuxianClient = new XiuxianMUDClient();
});
```

### 3. Django模板实现

#### 主界面模板 (webclient.html)
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仙侠MUD - AI导演驱动修仙世界</title>
    <link rel="stylesheet" href="/static/webclient/css/xiuxian.css">
</head>
<body>
    <div class="xiuxian-container">
        <!-- 顶部状态栏 -->
        <header class="top-status-bar">
            <div class="game-title">🎭 仙侠MUD - AI导演驱动修仙世界</div>
            <div class="status-info">
                <span id="online-status">状态：在线</span>
                <span id="character-realm">境界：筑基三层</span>
                <span id="sect-info">门派：青云门</span>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧面板 -->
            <aside class="left-panel">
                {% include "webclient/partials/character_status.html" %}
                {% include "webclient/partials/quick_actions.html" %}
            </aside>

            <!-- 中央显示区 -->
            <section class="center-panel">
                <div id="game-output" class="game-output-area"></div>
                
                <div class="input-area">
                    <div class="command-input-container">
                        <input type="text" id="command-input" placeholder="输入命令...">
                        <button id="send-button">发送</button>
                    </div>
                </div>
            </section>

            <!-- 右侧面板 -->
            <aside class="right-panel">
                {% include "webclient/partials/ai_director_panel.html" %}
                {% include "webclient/partials/chat_channels.html" %}
            </aside>
        </main>
    </div>

    <script src="/static/webclient/js/xiuxian_client.js"></script>
</body>
</html>
```

## 📅 实施计划

### 第一阶段：基础框架 (1周)
- [x] 创建Django模板结构
- [x] 实现基础CSS样式
- [x] 建立WebSocket通信
- [x] 基础命令处理

### 第二阶段：界面完善 (1周)  
- [ ] 完善仙侠主题样式
- [ ] 实现侧边栏组件
- [ ] 添加快捷操作功能
- [ ] 优化输入体验

### 第三阶段：AI导演集成 (1周)
- [ ] 实现AI导演面板
- [ ] 基础AI消息处理
- [ ] 简单的剧情状态管理
- [ ] AI建议系统

### 第四阶段：功能完善 (1周)
- [ ] 聊天频道系统
- [ ] 在线玩家显示
- [ ] 命令历史和补全
- [ ] 最终测试和优化

## 📊 性能指标

### 目标指标 (10人并发)
- 页面加载时间：< 2秒
- WebSocket连接延迟：< 100ms
- 内存使用：< 50MB (客户端)
- 服务器响应时间：< 200ms

### 简化原则
- 无复杂框架依赖
- 最小化JavaScript文件大小
- 减少HTTP请求数量
- 优化WebSocket消息频率

## 🔒 安全考虑

### 基础安全措施
- Django CSRF保护
- WebSocket连接验证
- 输入命令过滤
- 基础XSS防护

### 简化策略
- 不实施复杂的安全协议
- 依赖Evennia内置安全机制
- 专注于基本的输入验证
- 10人规模无需高级防护

## 📝 总结

本设计方案严格遵循用户需求：

1. **桌面端专用**：专注1920x1080桌面体验
2. **Evennia原生**：最大化利用Django模板和WebSocket
3. **简洁实用**：避免过度工程化和复杂优化
4. **小规模友好**：适合10人左右并发使用

通过4周的渐进式开发，可以实现一个简洁、实用、具有仙侠特色的MUD Web客户端，既保持了传统MUD的文字游戏特色，又融入了现代Web技术和基础的AI导演功能。 