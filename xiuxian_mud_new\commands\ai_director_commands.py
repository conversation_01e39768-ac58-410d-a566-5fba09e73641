"""
AI导演相关命令
"""

from commands.command import BaseCommand
from evennia import CmdSet

try:
    from systems.ai_director import get_ai_director
    from systems.handlers.ai_director_handler import AIDirectorHandler
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False


class CmdAIDirectorStatus(BaseCommand):
    """
    查看AI导演状态
    
    用法:
        ai状态
        aidirector
    """
    
    key = "ai状态"
    aliases = ["aidirector", "ai_status"]
    locks = "cmd:all()"
    help_category = "游戏系统"
    
    def func(self):
        if not AI_AVAILABLE:
            self.caller.msg("AI导演系统未启用。")
            return
        
        # 获取AI导演Handler
        if hasattr(self.caller, 'ai_director'):
            handler = self.caller.ai_director
            
            # 获取状态
            status = handler.get_story_status()
            
            self.caller.msg(f"""
|y=================== AI导演状态 ===================|n
|g活跃事件:|n {status['active_events']} 个
|g完成事件:|n {status['completed_events']} 个
|g当前纪元:|n {status['world_era']}
|g灵气水平:|n {status['spiritual_energy']:.2f}
|g天象状态:|n {status['celestial_alignment']}
|g叙事风格:|n {status['narrative_style']}
|g剧情复杂度:|n {status['complexity']:.1f}
|g事件频率:|n {status['frequency']:.1f}
|g玩家主导性:|n {status['player_agency']:.1f}
|y================================================|n
            """)
            
            # 获取性能统计
            perf = handler.get_ai_performance_stats()
            if perf and 'ai_stats' in perf:
                stats = perf['ai_stats']
                self.caller.msg(f"""
|y================== 性能统计 ==================|n
|g总决策数:|n {stats.get('total_decisions', 0)}
|g平均响应时间:|n {stats.get('average_response_time', 0)*1000:.1f}ms
|g缓存命中率:|n {(stats.get('cache_hits', 0)/(stats.get('cache_hits', 0)+stats.get('cache_misses', 1))*100):.1f}%
|y============================================|n
                """)
        else:
            self.caller.msg("您还没有AI导演Handler。")


class CmdParseStoryOutline(BaseCommand):
    """
    解析故事大纲
    
    用法:
        解析故事 <大纲文本>
        parse_story <outline>
    
    示例:
        解析故事 《逆天改命》主题：凡人修仙...
    """
    
    key = "解析故事"
    aliases = ["parse_story"]
    locks = "cmd:perm(Builder)"
    help_category = "游戏系统"
    
    def func(self):
        if not self.args:
            self.caller.msg("用法: 解析故事 <大纲文本>")
            return
        
        if not AI_AVAILABLE:
            self.caller.msg("AI导演系统未启用。")
            return
        
        if hasattr(self.caller, 'ai_director'):
            handler = self.caller.ai_director
            
            self.caller.msg("|y正在解析故事大纲...|n")
            
            result = handler.parse_story_outline(self.args)
            
            if result['success']:
                self.caller.msg(f"""
|g解析成功！|n
|y标题:|n {result['title']}
|y主题:|n {result['theme']}
|y核心冲突:|n {result['main_conflict']}
|y关键角色:|n {', '.join(result['key_characters'])}
|y剧情点数:|n {result['plot_points']}
|y故事阶段:|n {' → '.join(result['phases'])}
                """)
            else:
                self.caller.msg(f"|r解析失败:|n {result.get('error', '未知错误')}")
        else:
            self.caller.msg("您还没有AI导演Handler。")


class CmdAIDirectorTest(BaseCommand):
    """
    测试AI导演功能
    
    用法:
        测试ai
        test_ai
    """
    
    key = "测试ai"
    aliases = ["test_ai"]
    locks = "cmd:perm(Developer)"
    help_category = "游戏系统"
    
    def func(self):
        if not AI_AVAILABLE:
            self.caller.msg("AI导演系统未启用。")
            return
        
        self.caller.msg("|y开始测试AI导演功能...|n")
        
        # 测试AI导演
        director = get_ai_director()
        
        # 测试事件响应
        test_event = {
            "event_type": "cultivation_breakthrough",
            "character": self.caller.name,
            "location": self.caller.location.name if self.caller.location else "未知地点",
            "description": f"{self.caller.name}成功突破境界"
        }
        
        decision = director.make_decision(test_event)
        
        self.caller.msg(f"""
|g测试结果:|n
|y决策类型:|n {decision.decision_type.value}
|y决策内容:|n {decision.content}
|y置信度:|n {decision.confidence:.2f}
|y响应时间:|n {decision.response_time*1000:.1f}ms
        """)


class AIDirectorCmdSet(CmdSet):
    """AI导演命令集"""
    
    def at_cmdset_creation(self):
        self.add(CmdAIDirectorStatus())
        self.add(CmdParseStoryOutline())
        self.add(CmdAIDirectorTest())