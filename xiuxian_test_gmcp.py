#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
仙侠MUD GMCP功能测试脚本
使用Playwright MCP测试所有仙侠系统的GMCP集成
"""

import asyncio
import json
import time
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'xiuxian_mud_new'))

# 配置Django设置
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
import django
django.setup()

class XiuxianGMCPTest:
    def __init__(self):
        self.test_results = []
        
    async def test_cultivation_handler(self):
        """测试修炼系统Handler"""
        print("🧘‍♂️ 测试修炼系统Handler...")
        
        try:
            # 导入修炼Handler
            from xiuxian_mud_new.systems.handlers.cultivation_handler import CultivationHandler
            
            # 创建测试实例
            handler = CultivationHandler()
            
            # 测试基本功能
            test_data = {
                "character_id": "test_char_001",
                "realm": "炼气期",
                "level": 1,
                "progress": 0
            }
            
            # 测试获取境界信息
            realm_info = handler.get_realm_info(test_data["realm"])
            print(f"  ✅ 获取境界信息: {realm_info}")
            
            # 测试修炼进度计算
            progress = handler.calculate_cultivation_progress(test_data)
            print(f"  ✅ 修炼进度计算: {progress}")
            
            # 测试突破检查
            can_breakthrough = handler.can_breakthrough(test_data)
            print(f"  ✅ 突破检查: {can_breakthrough}")
            
            self.test_results.append({
                "system": "修炼系统",
                "status": "成功",
                "details": f"境界: {realm_info}, 进度: {progress}, 可突破: {can_breakthrough}"
            })
            
        except Exception as e:
            print(f"  ❌ 修炼系统测试失败: {e}")
            self.test_results.append({
                "system": "修炼系统",
                "status": "失败",
                "error": str(e)
            })
    
    async def test_combat_skill_handler(self):
        """测试战斗技能系统Handler"""
        print("\n⚔️ 测试战斗技能系统Handler...")
        
        try:
            from xiuxian_mud_new.systems.handlers.combat_skill_handler import CombatSkillHandler
            
            handler = CombatSkillHandler()
            
            test_data = {
                "character_id": "test_char_001",
                "skills": ["火球术", "冰锥术"],
                "skill_levels": {"火球术": 3, "冰锥术": 1}
            }
            
            # 测试技能列表
            available_skills = handler.get_available_skills()
            print(f"  ✅ 可用技能: {len(available_skills)} 个")
            
            # 测试技能学习
            learn_result = handler.learn_skill(test_data["character_id"], "火球术")
            print(f"  ✅ 学习技能: {learn_result}")
            
            # 测试技能使用
            use_result = handler.use_skill(test_data["character_id"], "火球术")
            print(f"  ✅ 使用技能: {use_result}")
            
            self.test_results.append({
                "system": "战斗技能系统",
                "status": "成功",
                "details": f"可用技能: {len(available_skills)}, 学习: {learn_result}, 使用: {use_result}"
            })
            
        except Exception as e:
            print(f"  ❌ 战斗技能系统测试失败: {e}")
            self.test_results.append({
                "system": "战斗技能系统",
                "status": "失败",
                "error": str(e)
            })
    
    async def test_alchemy_handler(self):
        """测试炼丹系统Handler"""
        print("\n🧪 测试炼丹系统Handler...")
        
        try:
            from xiuxian_mud_new.systems.handlers.alchemy_handler import AlchemyHandler
            
            handler = AlchemyHandler()
            
            test_data = {
                "character_id": "test_char_001",
                "materials": {"灵石": 10, "灵草": 5},
                "recipes": ["回血丹", "回蓝丹"]
            }
            
            # 测试配方列表
            recipes = handler.get_recipes()
            print(f"  ✅ 可用配方: {len(recipes)} 个")
            
            # 测试材料检查
            materials_check = handler.check_materials(test_data["character_id"], "回血丹")
            print(f"  ✅ 材料检查: {materials_check}")
            
            # 测试炼丹过程
            alchemy_result = handler.craft_pill(test_data["character_id"], "回血丹")
            print(f"  ✅ 炼丹结果: {alchemy_result}")
            
            self.test_results.append({
                "system": "炼丹系统",
                "status": "成功",
                "details": f"配方: {len(recipes)}, 材料: {materials_check}, 炼丹: {alchemy_result}"
            })
            
        except Exception as e:
            print(f"  ❌ 炼丹系统测试失败: {e}")
            self.test_results.append({
                "system": "炼丹系统",
                "status": "失败",
                "error": str(e)
            })
    
    async def test_karma_handler(self):
        """测试因果系统Handler"""
        print("\n⚖️ 测试因果系统Handler...")
        
        try:
            from xiuxian_mud_new.systems.handlers.karma_handler import KarmaHandler
            
            handler = KarmaHandler()
            
            test_data = {
                "character_id": "test_char_001",
                "karma_points": 100,
                "karma_type": "good"
            }
            
            # 测试因果状态
            karma_status = handler.get_karma_status(test_data["character_id"])
            print(f"  ✅ 因果状态: {karma_status}")
            
            # 测试记录善行
            good_karma = handler.record_karma(test_data["character_id"], "good", 10, "帮助他人")
            print(f"  ✅ 记录善行: {good_karma}")
            
            # 测试记录恶行
            evil_karma = handler.record_karma(test_data["character_id"], "evil", 5, "伤害无辜")
            print(f"  ✅ 记录恶行: {evil_karma}")
            
            self.test_results.append({
                "system": "因果系统",
                "status": "成功",
                "details": f"状态: {karma_status}, 善行: {good_karma}, 恶行: {evil_karma}"
            })
            
        except Exception as e:
            print(f"  ❌ 因果系统测试失败: {e}")
            self.test_results.append({
                "system": "因果系统",
                "status": "失败",
                "error": str(e)
            })
    
    async def test_ai_director_handler(self):
        """测试AI导演系统Handler"""
        print("\n🎭 测试AI导演系统Handler...")
        
        try:
            from xiuxian_mud_new.systems.handlers.ai_director_handler import AIDirectorHandler
            
            handler = AIDirectorHandler()
            
            test_data = {
                "character_id": "test_char_001",
                "context": "角色开始修炼",
                "world_state": "peaceful"
            }
            
            # 测试故事状态
            story_status = handler.get_story_status()
            print(f"  ✅ 故事状态: {story_status}")
            
            # 测试世界状态
            world_state = handler.get_world_state()
            print(f"  ✅ 世界状态: {world_state}")
            
            # 测试上下文更新
            context_update = handler.update_context(test_data["character_id"], test_data["context"])
            print(f"  ✅ 上下文更新: {context_update}")
            
            self.test_results.append({
                "system": "AI导演系统",
                "status": "成功",
                "details": f"故事: {story_status}, 世界: {world_state}, 上下文: {context_update}"
            })
            
        except Exception as e:
            print(f"  ❌ AI导演系统测试失败: {e}")
            self.test_results.append({
                "system": "AI导演系统",
                "status": "失败",
                "error": str(e)
            })
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 仙侠MUD Handler系统测试报告")
        print("="*60)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r["status"] == "成功"])
        failed_tests = len([r for r in self.test_results if r["status"] == "失败"])
        
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(successful_tests/total_tests*100):.1f}%")
        
        print("\n详细结果:")
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "成功" else "❌"
            print(f"{status_icon} {result['system']}: {result['status']}")
            if "error" in result:
                print(f"   错误: {result['error']}")
            elif "details" in result:
                print(f"   详情: {result['details']}")
        
        # 保存详细报告到文件
        with open("xiuxian_handler_test_report.json", "w", encoding="utf-8") as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: xiuxian_handler_test_report.json")
        
        if failed_tests == 0:
            print("\n🎉 所有Handler系统测试通过！仙侠MUD功能集成成功！")
        else:
            print(f"\n⚠️ 有 {failed_tests} 个系统测试失败，请检查相关功能。")

async def main():
    """主函数"""
    print("🧙‍♂️ 仙侠MUD Handler系统功能测试启动...")
    print("测试所有核心Handler系统的集成状态...")
    print("="*60)
    
    tester = XiuxianGMCPTest()
    
    # 运行所有Handler测试
    await tester.test_cultivation_handler()
    await tester.test_combat_skill_handler()
    await tester.test_alchemy_handler()
    await tester.test_karma_handler()
    await tester.test_ai_director_handler()
    
    # 生成测试报告
    tester.generate_report()

if __name__ == "__main__":
    asyncio.run(main())
