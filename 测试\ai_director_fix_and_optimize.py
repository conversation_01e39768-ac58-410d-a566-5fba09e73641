#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统问题修复和优化脚本
根据测试结果修复发现的问题并优化性能
"""

import os
import sys
import time
from pathlib import Path

class AIDirectorOptimizer:
    """AI导演系统优化器"""
    
    def __init__(self):
        self.testgame_dir = Path("testgame")
        self.fixes_applied = []
    
    def log_fix(self, fix_name, success, message=""):
        """记录修复结果"""
        result = {
            "fix": fix_name,
            "success": success,
            "message": message,
            "timestamp": time.strftime("%H:%M:%S")
        }
        self.fixes_applied.append(result)
        status = "✅" if success else "❌"
        print(f"{status} {fix_name}: {message}")
    
    def fix_api_key_configuration(self):
        """修复API密钥配置问题"""
        try:
            settings_path = self.testgame_dir / "server" / "conf" / "settings.py"
            
            if not settings_path.exists():
                self.log_fix("API密钥配置", False, "settings.py文件不存在")
                return False
            
            # 读取现有设置
            with open(settings_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否需要添加模拟配置
            if "AI_USE_MOCK_CLIENT" not in content:
                mock_config = '''

# AI导演系统优化配置
AI_USE_MOCK_CLIENT = True  # 使用模拟客户端进行演示
AI_AVAILABLE = True        # 启用AI导演系统

# 性能优化配置
AI_CACHE_ENABLED = True
AI_CACHE_TIMEOUT = 300     # 缓存5分钟

# 调试配置
AI_DEBUG = True
AI_LOG_DECISIONS = True
'''
                
                with open(settings_path, 'a', encoding='utf-8') as f:
                    f.write(mock_config)
                
                self.log_fix("API密钥配置", True, "添加了模拟客户端配置")
            else:
                self.log_fix("API密钥配置", True, "配置已存在")
            
            return True
            
        except Exception as e:
            self.log_fix("API密钥配置", False, f"配置失败: {str(e)}")
            return False
    
    def optimize_ai_client(self):
        """优化AI客户端配置"""
        try:
            ai_client_path = self.testgame_dir / "systems" / "ai_client.py"
            
            if not ai_client_path.exists():
                self.log_fix("AI客户端优化", False, "ai_client.py文件不存在")
                return False
            
            # 读取AI客户端文件
            with open(ai_client_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否需要添加模拟客户端支持
            if "AI_USE_MOCK_CLIENT" not in content:
                # 在get_ai_client函数中添加模拟客户端逻辑
                optimized_function = '''
def get_ai_client():
    """获取AI客户端实例"""
    global _ai_client
    
    if _ai_client is None:
        try:
            # 检查是否使用模拟客户端
            from django.conf import settings
            if getattr(settings, 'AI_USE_MOCK_CLIENT', False):
                print("🎭 使用模拟AI客户端")
                _ai_client = MockAIClient()
            else:
                _ai_client = MindCraftAIClient()
        except Exception as e:
            print(f"⚠️ AI客户端初始化失败，使用模拟客户端: {e}")
            _ai_client = MockAIClient()
    
    return _ai_client
'''
                
                # 替换原有的get_ai_client函数
                lines = content.split('\n')
                new_lines = []
                skip_lines = False
                
                for line in lines:
                    if line.strip().startswith('def get_ai_client():'):
                        skip_lines = True
                        new_lines.extend(optimized_function.strip().split('\n'))
                    elif skip_lines and line.strip() and not line.startswith(' ') and not line.startswith('\t'):
                        skip_lines = False
                        new_lines.append(line)
                    elif not skip_lines:
                        new_lines.append(line)
                
                # 写回文件
                with open(ai_client_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(new_lines))
                
                self.log_fix("AI客户端优化", True, "添加了模拟客户端支持")
            else:
                self.log_fix("AI客户端优化", True, "优化已存在")
            
            return True
            
        except Exception as e:
            self.log_fix("AI客户端优化", False, f"优化失败: {str(e)}")
            return False
    
    def add_performance_monitoring(self):
        """添加性能监控"""
        try:
            monitor_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统性能监控脚本
"""

import time
import requests
import json
from datetime import datetime

def monitor_api_performance():
    """监控API性能"""
    base_url = "http://localhost:4001"
    
    print(f"🔍 AI导演系统性能监控")
    print(f"   开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 测试API端点
    endpoints = [
        "/api/ai-director/status/",
        "/api/ai-director/parse-outline/",
        "/api/ai-director/make-decision/"
    ]
    
    for endpoint in endpoints:
        url = base_url + endpoint
        
        try:
            start_time = time.time()
            
            if endpoint.endswith("status/"):
                response = requests.get(url, timeout=10)
            else:
                test_data = {"test": True}
                response = requests.post(url, json=test_data, timeout=10)
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            print(f"📊 {endpoint}")
            print(f"   状态码: {response.status_code}")
            print(f"   响应时间: {response_time:.1f}ms")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应: {data.get('success', 'N/A')}")
                except:
                    print(f"   响应: 非JSON格式")
            
            print()
            
        except Exception as e:
            print(f"❌ {endpoint}: 请求失败 - {e}")
            print()

if __name__ == "__main__":
    monitor_api_performance()
'''
            
            monitor_path = self.testgame_dir / "monitor_ai_director.py"
            with open(monitor_path, 'w', encoding='utf-8') as f:
                f.write(monitor_script)
            
            self.log_fix("性能监控", True, f"创建监控脚本: {monitor_path}")
            return True
            
        except Exception as e:
            self.log_fix("性能监控", False, f"创建失败: {str(e)}")
            return False
    
    def create_demo_script(self):
        """创建演示脚本"""
        try:
            demo_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统演示脚本
展示AI导演功能的完整工作流程
"""

import requests
import json
import time

def demo_ai_director():
    """演示AI导演功能"""
    base_url = "http://localhost:4001"
    
    print("🎭 AI导演系统功能演示")
    print("=" * 50)
    
    # 1. 检查系统状态
    print("\\n1. 检查AI导演系统状态...")
    try:
        response = requests.get(f"{base_url}/api/ai-director/status/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 系统状态: {data.get('status', '未知')}")
        else:
            print(f"   ❌ 状态检查失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        return
    
    # 2. 演示故事大纲解析
    print("\\n2. 演示故事大纲解析...")
    story_outline = {
        "outline_text": """
        《逆天改命》
        主题：凡人逆天修仙，挑战命运束缚
        核心冲突：废灵根修士林逸风与天道的对抗
        主要角色：
        - 林逸风：主角，废灵根却有惊人悟性
        - 苏清雪：青云宗天才弟子，后成为道侣
        - 魔君血煞：最终反派，代表天道意志
        
        剧情要点：
        1. 序章：林逸风觉醒特殊体质
        2. 起承：拜入青云宗，遇到苏清雪
        3. 高潮：各方势力争夺逆天秘密
        4. 转合：与天道直接对抗
        5. 终章：改变修仙界格局
        """
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/ai-director/parse-outline/",
            json=story_outline,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ 解析成功!")
                print(f"   📖 故事标题: {data.get('title', '未知')}")
                print(f"   🎯 主题: {data.get('theme', '未知')}")
                print(f"   ⚔️ 核心冲突: {data.get('main_conflict', '未知')}")
                print(f"   👥 关键角色: {', '.join(data.get('key_characters', []))}")
            else:
                print(f"   ❌ 解析失败: {data.get('error', '未知错误')}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 解析异常: {e}")
    
    # 3. 演示AI决策生成
    print("\\n3. 演示AI决策生成...")
    
    test_events = [
        {
            "event_type": "CultivationBreakthrough",
            "character": "林逸风",
            "description": "主角成功突破到筑基期，天地灵气汇聚",
            "location": "青云峰后山",
            "priority": "HIGH"
        },
        {
            "event_type": "CharacterMeeting", 
            "character": "苏清雪",
            "description": "苏清雪在藏书阁遇到林逸风",
            "location": "青云宗藏书阁",
            "priority": "MEDIUM"
        },
        {
            "event_type": "ConflictEscalation",
            "character": "魔君血煞",
            "description": "魔君血煞感知到逆天之力的觉醒",
            "location": "魔域深渊",
            "priority": "HIGH"
        }
    ]
    
    for i, event in enumerate(test_events, 1):
        print(f"\\n   事件 {i}: {event['event_type']}")
        try:
            response = requests.post(
                f"{base_url}/api/ai-director/make-decision/",
                json=event,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"   ✅ 决策生成成功!")
                    print(f"   🎭 决策类型: {data.get('decision_type', '未知')}")
                    print(f"   📝 决策内容: {data.get('content', '无内容')[:100]}...")
                    print(f"   ⏱️ 响应时间: {data.get('response_time', 0):.1f}ms")
                    print(f"   🎯 置信度: {data.get('confidence', 0):.2f}")
                else:
                    print(f"   ❌ 决策失败: {data.get('error', '未知错误')}")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 决策异常: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    print("\\n🎉 AI导演系统演示完成!")
    print("\\n💡 提示:")
    print("   - 所有功能都在模拟模式下运行")
    print("   - 配置真实API密钥可获得更智能的响应")
    print("   - 系统支持实时剧情生成和角色互动")

if __name__ == "__main__":
    demo_ai_director()
'''
            
            demo_path = self.testgame_dir / "demo_ai_director.py"
            with open(demo_path, 'w', encoding='utf-8') as f:
                f.write(demo_script)
            
            self.log_fix("演示脚本", True, f"创建演示脚本: {demo_path}")
            return True
            
        except Exception as e:
            self.log_fix("演示脚本", False, f"创建失败: {str(e)}")
            return False
    
    def run_optimization(self):
        """运行完整优化流程"""
        print("🔧 开始AI导演系统优化")
        print(f"   目标目录: {self.testgame_dir}")
        print(f"   开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 执行优化步骤
        optimizations = [
            ("API密钥配置修复", self.fix_api_key_configuration),
            ("AI客户端优化", self.optimize_ai_client),
            ("性能监控添加", self.add_performance_monitoring),
            ("演示脚本创建", self.create_demo_script)
        ]
        
        for opt_name, opt_func in optimizations:
            print(f"\\n🔧 执行优化: {opt_name}")
            opt_func()
        
        # 输出优化总结
        self.print_summary()
    
    def print_summary(self):
        """打印优化总结"""
        print("\\n" + "=" * 60)
        print("优化总结")
        print("=" * 60)
        
        passed = sum(1 for result in self.fixes_applied if result['success'])
        total = len(self.fixes_applied)
        
        for result in self.fixes_applied:
            status = "✅ 成功" if result['success'] else "❌ 失败"
            print(f"{result['fix']}: {status}")
            if result['message']:
                print(f"   {result['message']}")
        
        print(f"\\n总计: {passed}/{total} 优化成功")
        
        if passed == total:
            print("\\n🎉 AI导演系统优化完成!")
            print("\\n📋 下一步操作:")
            print("1. 重启Evennia服务器: python -m evennia restart")
            print("2. 运行演示脚本: python demo_ai_director.py")
            print("3. 运行性能监控: python monitor_ai_director.py")
            print("4. 访问Web界面: http://localhost:4001/webclient/")
        else:
            print(f"\\n⚠️ 有 {total - passed} 个优化失败")
            print("请检查错误信息并手动修复问题")


def main():
    """主函数"""
    optimizer = AIDirectorOptimizer()
    optimizer.run_optimization()


if __name__ == "__main__":
    main()
