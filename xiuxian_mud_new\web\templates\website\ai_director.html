{% extends "website/base.html" %}

{% block content %}
<div class="container mt-4">
    <h1 class="text-center mb-4">🎭 AI导演系统</h1>
    
    <!-- 系统状态 -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>系统状态</h3>
        </div>
        <div class="card-body">
            <div id="systemStatus" class="alert alert-success">
                ✅ AI导演系统运行正常
            </div>
        </div>
    </div>
    
    <!-- 故事大纲解析 -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>故事大纲解析</h3>
        </div>
        <div class="card-body">
            <textarea id="storyOutline" class="form-control mb-3" rows="10" placeholder="输入故事大纲...">《逆天改命》

主题：凡人逆天修仙，挑战命运束缚

核心冲突：主角作为废灵根的凡人，在修仙界备受歧视，但通过特殊机缘获得了改变命运的机会，与各大宗门、天道意志产生冲突。

主要角色：
- 林逸风：主角，废灵根却有惊人悟性
- 苏清雪：青云宗天才弟子，主角的引路人
- 魔君血煞：上古魔头，与主角有因果纠葛

剧情要点：
1. 序章：林逸风在凡人村庄遭遇妖兽袭击，意外觉醒特殊体质
2. 起承：拜入青云宗外门，受尽欺凌但坚持修炼
3. 高潮：各方势力争夺主角身上的秘密，大战爆发</textarea>
            <button class="btn btn-primary" onclick="parseOutline()">🔍 解析故事大纲</button>
            <div id="parseResult" class="mt-3"></div>
        </div>
    </div>
    
    <!-- AI决策测试 -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>AI决策测试</h3>
        </div>
        <div class="card-body">
            <div class="btn-group mb-3" role="group">
                <button class="btn btn-warning" onclick="testDecision('cultivation_breakthrough')">⚡ 修炼突破</button>
                <button class="btn btn-danger" onclick="testDecision('combat_event')">⚔️ 战斗事件</button>
                <button class="btn btn-info" onclick="testDecision('treasure_discovery')">💎 发现宝物</button>
                <button class="btn btn-secondary" onclick="testDecision('sect_conflict')">🏛️ 宗门冲突</button>
            </div>
            <div id="decisionResult"></div>
        </div>
    </div>
    
    <!-- 性能统计 -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>性能统计</h3>
        </div>
        <div class="card-body">
            <button class="btn btn-success mb-3" onclick="getStats()">📊 刷新统计</button>
            <div id="statsDisplay" class="row"></div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.card-header {
    background-color: #f8f9fa;
    font-weight: bold;
}
.stat-card {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 15px;
}
.stat-value {
    font-size: 28px;
    font-weight: bold;
    color: #007bff;
}
.response-box {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-top: 15px;
}
</style>

<script>
// CSRF token获取
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

const csrftoken = getCookie('csrftoken');

async function parseOutline() {
    const outline = document.getElementById('storyOutline').value;
    const resultDiv = document.getElementById('parseResult');
    
    resultDiv.innerHTML = '<div class="alert alert-info">正在解析...</div>';
    
    try {
        const response = await fetch('/api/ai-director/parse-outline/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrftoken
            },
            body: JSON.stringify({outline_text: outline})
        });
        
        const data = await response.json();
        
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">✅ 解析成功！</div>
                <div class="response-box">
                    <p><strong>标题：</strong>${data.title}</p>
                    <p><strong>主题：</strong>${data.theme}</p>
                    <p><strong>核心冲突：</strong>${data.main_conflict}</p>
                    <p><strong>关键角色：</strong>${data.key_characters.join('、')}</p>
                    <p><strong>剧情点数：</strong>${data.plot_points}</p>
                    <p><strong>故事阶段：</strong>${data.phases.join(' → ')}</p>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `<div class="alert alert-danger">❌ 解析失败：${data.error}</div>`;
        }
    } catch (error) {
        resultDiv.innerHTML = `<div class="alert alert-danger">❌ 请求失败：${error.message}</div>`;
    }
}

async function testDecision(eventType) {
    const resultDiv = document.getElementById('decisionResult');
    resultDiv.innerHTML = '<div class="alert alert-info">AI正在思考...</div>';
    
    const eventData = {
        event_type: eventType,
        character: '{{ user.username|default:"测试角色" }}',
        location: '青云峰',
        description: getEventDescription(eventType)
    };
    
    try {
        const response = await fetch('/api/ai-director/make-decision/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrftoken
            },
            body: JSON.stringify(eventData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">✅ AI决策完成！</div>
                <div class="response-box">
                    <p><strong>决策类型：</strong>${data.decision_type}</p>
                    <p><strong>决策内容：</strong>${data.content}</p>
                    <p><strong>置信度：</strong>${(data.confidence * 100).toFixed(1)}%</p>
                    <p><strong>响应时间：</strong>${data.response_time.toFixed(1)}ms</p>
                    ${data.next_actions && data.next_actions.length > 0 ? `
                        <p><strong>后续建议：</strong></p>
                        <ul>${data.next_actions.map(a => '<li>' + a + '</li>').join('')}</ul>
                    ` : ''}
                </div>
            `;
        } else {
            resultDiv.innerHTML = `<div class="alert alert-danger">❌ 决策失败：${data.error}</div>`;
        }
    } catch (error) {
        resultDiv.innerHTML = `<div class="alert alert-danger">❌ 请求失败：${error.message}</div>`;
    }
}

function getEventDescription(eventType) {
    const descriptions = {
        'cultivation_breakthrough': '成功突破到筑基期，引起天地异象',
        'combat_event': '与内门弟子发生冲突，展现惊人实力',
        'treasure_discovery': '在秘境中发现上古法宝',
        'sect_conflict': '青云宗与魔道爆发冲突'
    };
    return descriptions[eventType] || '发生了重要事件';
}

async function getStats() {
    const statsDiv = document.getElementById('statsDisplay');
    
    try {
        const response = await fetch('/api/ai-director/stats/');
        const data = await response.json();
        
        if (data.success) {
            statsDiv.innerHTML = `
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value">${data.total_decisions}</div>
                        <div>总决策数</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value">${data.avg_response_time.toFixed(1)}ms</div>
                        <div>平均响应时间</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value">${data.cache_hit_rate.toFixed(1)}%</div>
                        <div>缓存命中率</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value">${data.cache_hits}/${data.cache_misses}</div>
                        <div>命中/未命中</div>
                    </div>
                </div>
            `;
        } else {
            statsDiv.innerHTML = '<div class="col-12"><div class="alert alert-danger">获取统计失败</div></div>';
        }
    } catch (error) {
        statsDiv.innerHTML = `<div class="col-12"><div class="alert alert-danger">请求失败：${error.message}</div></div>`;
    }
}

// 页面加载完成后获取初始统计
window.onload = function() {
    getStats();
};
</script>
{% endblock %}