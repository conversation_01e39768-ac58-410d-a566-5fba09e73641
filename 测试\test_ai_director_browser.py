#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
浏览器端AI导演功能测试脚本
模拟修炼突破事件，触发AI导演响应
"""

import os
import sys
import time
import django

# 设置Django环境
if not os.path.exists('server'):
    print("❌ 请在xiuxian_mud_new目录下运行此脚本")
    sys.exit(1)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
django.setup()

def test_ai_director_live():
    """实时测试AI导演功能"""
    print("🎭 浏览器端AI导演功能测试")
    print("=" * 50)
    
    try:
        # 1. 获取当前连接的用户
        from evennia import search_account
        testuser = search_account("testuser")
        if not testuser:
            print("❌ 未找到testuser账户")
            return
        
        print(f"✅ 找到用户: {testuser}")
        
        # 2. 获取角色
        character = testuser.get_puppet(testuser.sessions.all()[0])
        if not character:
            character = testuser.db.playable_characters[0] if testuser.db.playable_characters else None
        
        if not character:
            print("❌ 未找到角色")
            return
            
        print(f"✅ 找到角色: {character}")
        
        # 3. 导入事件系统
        from systems.event_system import XianxiaEventBus, CultivationBreakthroughEvent, EventPriority
        
        # 4. 获取事件总线
        event_bus = XianxiaEventBus.get_instance()
        print(f"✅ 事件总线: {event_bus}")
        print(f"✅ 已注册处理器: {len(event_bus.handlers)}")
        
        # 5. 创建修炼突破事件
        breakthrough_event = CultivationBreakthroughEvent(
            character_id=str(character.id),
            character_name=character.name,
            from_level="炼气期",
            to_level="筑基期",
            description="testuser成功从炼气期突破到筑基期，天地灵气汇聚，紫气东来",
            location=str(character.location)
        )
        
        print(f"✅ 创建突破事件: {breakthrough_event}")
        
        # 6. 触发事件
        print("\n🚀 触发修炼突破事件...")
        responses = event_bus.publish_event(
            breakthrough_event, 
            priority=EventPriority.HIGH
        )
        
        print(f"✅ 事件已发布，响应数量: {len(responses)}")
        
        # 7. 显示AI导演响应
        for i, response in enumerate(responses, 1):
            print(f"\n📝 响应 {i}:")
            if isinstance(response, dict):
                print(f"   类型: {response.get('decision_type', '未知')}")
                print(f"   内容: {response.get('content', '无内容')[:100]}...")
            else:
                print(f"   响应: {str(response)[:100]}...")
        
        # 8. 向用户发送消息
        character.msg(f"|r【AI导演】|n 天地灵气汇聚，你的修炼突破引起了天道的注意...")
        character.msg(f"|y【系统】|n AI导演功能测试完成，共收到 {len(responses)} 个响应")
        
        print(f"\n🎉 AI导演测试完成！")
        print(f"   发布耗时: {time.time() - start_time:.3f}秒")
        print(f"   响应数量: {len(responses)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    start_time = time.time()
    test_ai_director_live() 