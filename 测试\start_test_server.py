#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动测试服务器 - 展示AI导演功能
"""

import os
import sys
import json
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入AI导演系统
from systems.ai_director import AIDirector, get_ai_director
from systems.handlers.ai_director_handler import AIDirectorHandler

print("✓ 成功导入AI导演模块")

# 全局AI导演实例
ai_director = get_ai_director()

# 测试数据存储
test_data = {
    "story_outlines": {},
    "decisions": [],
    "performance_stats": {}
}

class AIDirectorWebHandler(BaseHTTPRequestHandler):
    """Web请求处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.serve_main_page()
        elif parsed_path.path == '/api/status':
            self.get_status()
        else:
            self.send_error(404)
    
    def do_POST(self):
        """处理POST请求"""
        parsed_path = urlparse(self.path)
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
        except:
            data = {}
        
        if parsed_path.path == '/api/parse_outline':
            self.parse_story_outline(data)
        elif parsed_path.path == '/api/make_decision':
            self.make_decision(data)
        elif parsed_path.path == '/api/get_stats':
            self.get_performance_stats()
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        """提供主页面"""
        html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>AI导演系统 - Evennia测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .panel {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: rgba(0,255,0,0.2);
            border: 1px solid #0f0;
        }
        .status.error {
            background: rgba(255,0,0,0.2);
            border: 1px solid #f00;
        }
        button {
            background: #ffd700;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        button:hover {
            background: #ffed4e;
        }
        textarea {
            width: 100%;
            min-height: 150px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            color: #fff;
            padding: 10px;
            border-radius: 5px;
        }
        .response {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .loading {
            color: #ffd700;
            font-style: italic;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .stat-card {
            background: rgba(255,255,255,0.05);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            color: #ffd700;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 AI导演系统测试 - Evennia集成</h1>
        
        <div class="panel">
            <h2>系统状态</h2>
            <div id="systemStatus" class="status success">
                ✅ AI导演系统运行正常 - 服务器地址: http://localhost:8888
            </div>
        </div>
        
        <div class="panel">
            <h2>1. 故事大纲解析测试</h2>
            <textarea id="storyOutline" placeholder="输入故事大纲...">《逆天改命》

主题：凡人逆天修仙，挑战命运束缚

核心冲突：主角作为废灵根的凡人，在修仙界备受歧视，但通过特殊机缘获得了改变命运的机会，与各大宗门、天道意志产生冲突。

主要角色：
- 林逸风：主角，废灵根却有惊人悟性
- 苏清雪：青云宗天才弟子，主角的引路人
- 魔君血煞：上古魔头，与主角有因果纠葛

剧情要点：
1. 序章：林逸风在凡人村庄遭遇妖兽袭击，意外觉醒特殊体质
2. 起承：拜入青云宗外门，受尽欺凌但坚持修炼
3. 高潮：各方势力争夺主角身上的秘密，大战爆发</textarea>
            <br>
            <button onclick="parseOutline()">🔍 解析故事大纲</button>
            <div id="parseResult" class="response"></div>
        </div>
        
        <div class="panel">
            <h2>2. AI决策测试</h2>
            <button onclick="testDecision('cultivation_breakthrough')">⚡ 修炼突破</button>
            <button onclick="testDecision('combat_event')">⚔️ 战斗事件</button>
            <button onclick="testDecision('treasure_discovery')">💎 发现宝物</button>
            <button onclick="testDecision('sect_conflict')">🏛️ 宗门冲突</button>
            <div id="decisionResult" class="response"></div>
        </div>
        
        <div class="panel">
            <h2>3. 性能统计</h2>
            <button onclick="getStats()">📊 刷新统计</button>
            <div id="statsDisplay" class="stats"></div>
        </div>
    </div>
    
    <script>
        const API_BASE = 'http://localhost:8888';
        
        async function parseOutline() {
            const outline = document.getElementById('storyOutline').value;
            const resultDiv = document.getElementById('parseResult');
            
            resultDiv.innerHTML = '<div class="loading">正在解析...</div>';
            
            try {
                const response = await fetch(API_BASE + '/api/parse_outline', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({outline_text: outline})
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="status success">✅ 解析成功！</div>
<strong>标题：</strong>${data.title}
<strong>主题：</strong>${data.theme}
<strong>核心冲突：</strong>${data.main_conflict}
<strong>关键角色：</strong>${data.key_characters.join('、')}
<strong>剧情点数：</strong>${data.plot_points}
<strong>故事阶段：</strong>${data.phases.join(' → ')}`;
                } else {
                    resultDiv.innerHTML = `<div class="status error">❌ 解析失败：${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="status error">❌ 请求失败：${error.message}</div>`;
            }
        }
        
        async function testDecision(eventType) {
            const resultDiv = document.getElementById('decisionResult');
            resultDiv.innerHTML = '<div class="loading">AI正在思考...</div>';
            
            const eventData = {
                event_type: eventType,
                character: '林逸风',
                location: '青云峰',
                description: getEventDescription(eventType)
            };
            
            try {
                const response = await fetch(API_BASE + '/api/make_decision', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(eventData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="status success">✅ AI决策完成！</div>
<strong>决策类型：</strong>${data.decision_type}
<strong>决策内容：</strong>${data.content}
<strong>置信度：</strong>${(data.confidence * 100).toFixed(1)}%
<strong>响应时间：</strong>${data.response_time.toFixed(1)}ms

<strong>后续建议：</strong>
${data.next_actions ? data.next_actions.map(a => '• ' + a).join('\\n') : '无'}`;
                } else {
                    resultDiv.innerHTML = `<div class="status error">❌ 决策失败：${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="status error">❌ 请求失败：${error.message}</div>`;
            }
        }
        
        function getEventDescription(eventType) {
            const descriptions = {
                'cultivation_breakthrough': '林逸风成功突破到筑基期，引起天地异象',
                'combat_event': '与内门弟子发生冲突，展现惊人实力',
                'treasure_discovery': '在秘境中发现上古法宝',
                'sect_conflict': '青云宗与魔道爆发冲突'
            };
            return descriptions[eventType] || '发生了重要事件';
        }
        
        async function getStats() {
            const statsDiv = document.getElementById('statsDisplay');
            
            try {
                const response = await fetch(API_BASE + '/api/get_stats', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    statsDiv.innerHTML = `
                        <div class="stat-card">
                            <div class="stat-value">${data.total_decisions}</div>
                            <div>总决策数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${data.avg_response_time.toFixed(1)}ms</div>
                            <div>平均响应时间</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${data.cache_hit_rate.toFixed(1)}%</div>
                            <div>缓存命中率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${data.active_outlines}</div>
                            <div>活跃故事线</div>
                        </div>
                    `;
                } else {
                    statsDiv.innerHTML = '<div class="status error">获取统计失败</div>';
                }
            } catch (error) {
                statsDiv.innerHTML = `<div class="status error">请求失败：${error.message}</div>`;
            }
        }
        
        // 页面加载完成后获取初始统计
        window.onload = function() {
            getStats();
        };
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def parse_story_outline(self, data):
        """解析故事大纲"""
        try:
            outline_text = data.get('outline_text', '')
            outline = ai_director.analyze_story_outline(outline_text)
            
            # 保存到测试数据
            test_data['story_outlines'][outline.outline_id] = outline
            
            response = {
                'success': True,
                'outline_id': outline.outline_id,
                'title': outline.title,
                'theme': outline.theme,
                'main_conflict': outline.main_conflict,
                'key_characters': outline.key_characters,
                'plot_points': len(outline.major_plot_points),
                'phases': [phase.value for phase in outline.expected_phases]
            }
        except Exception as e:
            response = {
                'success': False,
                'error': str(e)
            }
        
        self.send_json_response(response)
    
    def make_decision(self, data):
        """生成AI决策"""
        try:
            decision = ai_director.make_decision(data)
            
            # 保存决策记录
            test_data['decisions'].append(decision)
            
            response = {
                'success': True,
                'decision_id': decision.decision_id,
                'decision_type': decision.decision_type.value,
                'content': decision.content,
                'confidence': decision.confidence,
                'response_time': decision.response_time * 1000,  # 转换为毫秒
                'next_actions': decision.context.get('next_actions', [])
            }
        except Exception as e:
            response = {
                'success': False,
                'error': str(e)
            }
        
        self.send_json_response(response)
    
    def get_performance_stats(self):
        """获取性能统计"""
        stats = ai_director.get_performance_stats()
        
        cache_total = stats['cache_hits'] + stats['cache_misses']
        cache_hit_rate = (stats['cache_hits'] / cache_total * 100) if cache_total > 0 else 0
        
        response = {
            'success': True,
            'total_decisions': stats['total_decisions'],
            'avg_response_time': stats['average_response_time'] * 1000,
            'cache_hit_rate': cache_hit_rate,
            'active_outlines': len(test_data['story_outlines'])
        }
        
        self.send_json_response(response)
    
    def get_status(self):
        """获取系统状态"""
        response = {
            'status': 'running',
            'ai_director': 'active',
            'server_time': time.time()
        }
        self.send_json_response(response)
    
    def send_json_response(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定义日志消息"""
        pass  # 禁用默认日志

def start_server(port=8888):
    """启动测试服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, AIDirectorWebHandler)
    
    print(f"\n" + "="*60)
    print(f"🚀 AI导演测试服务器已启动")
    print(f"="*60)
    print(f"📍 访问地址: http://localhost:{port}")
    print(f"🎭 AI导演系统: 运行中")
    print(f"📊 性能监控: 已启用")
    print(f"\n按 Ctrl+C 停止服务器")
    print("="*60 + "\n")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == "__main__":
    start_server()