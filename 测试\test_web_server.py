#!/usr/bin/env python
"""
简单的Web测试服务器 - 用于测试Handler生态组件化框架
"""

import os
import sys
import json
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from systems.handler_system import (
        HandlerMemoryManager, HandlerCommunicationBus, HandlerDependencyManager,
        HandlerEventType, HandlerEvent, lazy_property, BaseHandler
    )
    print("✓ 成功导入Handler系统核心模块")

    try:
        from systems.handlers import CultivationHandler, AIDirectorHandler, KarmaHandler
        print("✓ 成功导入Handler实现模块")
    except ImportError as e:
        print(f"⚠️ Handler实现模块导入失败，使用基础Handler: {e}")
        # 使用基础Handler作为备选
        CultivationHandler = BaseHandler
        AIDirectorHandler = BaseHandler
        KarmaHandler = BaseHandler

except ImportError as e:
    print(f"✗ 导入Handler系统模块失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)


class TestCharacter:
    """测试角色类"""
    
    def __init__(self, name="测试修仙者"):
        self.key = name
        self.id = f"test_char_{int(time.time() * 1000)}"
        self.name = name
        self.attributes = MockAttributes()
        self.tags = MockTags()
    
    @lazy_property
    def cultivation(self):
        """修仙Handler"""
        return CultivationHandler(self)
    
    @lazy_property
    def karma(self):
        """因果Handler"""
        return KarmaHandler(self)
    
    @lazy_property
    def ai_director(self):
        """AI导演Handler"""
        return AIDirectorHandler(self)
    
    def msg(self, message):
        print(f"[{self.name}] {message}")


class MockAttributes:
    """模拟Attributes系统"""
    
    def __init__(self):
        self._data = {}
    
    def add(self, key, value, category=None):
        self._data[key] = value
    
    def get(self, key, default=None):
        return self._data.get(key, default)
    
    def has(self, key):
        return key in self._data


class MockTags:
    """模拟Tags系统"""
    
    def __init__(self):
        self.cultivation = MockCultivationTags()


class MockCultivationTags:
    """模拟修仙标签"""
    
    def set_realm(self, realm, level):
        print(f"设置境界: {realm} {level}级")


# 全局测试数据
test_characters = {}
test_results = {
    "handler_tests": [],
    "memory_stats": [],
    "event_history": [],
    "performance_data": []
}


class TestWebHandler(BaseHTTPRequestHandler):
    """Web测试处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        query = parse_qs(parsed_path.query)
        
        if path == '/':
            self.serve_main_page()
        elif path == '/api/create_character':
            self.create_test_character(query)
        elif path == '/api/test_handlers':
            self.test_handlers(query)
        elif path == '/api/memory_stats':
            self.get_memory_stats()
        elif path == '/api/event_history':
            self.get_event_history()
        elif path == '/api/performance_test':
            self.run_performance_test()
        elif path == '/api/cleanup_test':
            self.run_cleanup_test()
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        """提供主页面"""
        html = """
<!DOCTYPE html>
<html>
<head>
    <title>Handler生态组件化框架Web测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #005a87; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .stat-card { padding: 15px; background: #f8f9fa; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Handler生态组件化框架Web测试</h1>
        
        <div class="test-section">
            <h2>📊 测试控制面板</h2>
            <button class="button" onclick="createCharacter()">创建测试角色</button>
            <button class="button" onclick="testHandlers()">测试Handler功能</button>
            <button class="button" onclick="runPerformanceTest()">性能测试</button>
            <button class="button" onclick="runCleanupTest()">内存清理测试</button>
            <button class="button" onclick="refreshStats()">刷新统计</button>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>🧠 内存统计</h3>
                <div id="memory-stats">点击"刷新统计"获取数据</div>
            </div>
            
            <div class="stat-card">
                <h3>📡 事件历史</h3>
                <div id="event-history">点击"刷新统计"获取数据</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📋 测试结果</h2>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        function addResult(message, isSuccess = true) {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = 'result ' + (isSuccess ? 'success' : 'error');
            div.innerHTML = new Date().toLocaleTimeString() + ' - ' + message;
            results.insertBefore(div, results.firstChild);
        }

        function createCharacter() {
            fetch('/api/create_character?name=测试修仙者' + Date.now())
                .then(response => response.json())
                .then(data => {
                    addResult('✓ ' + data.message, data.success);
                })
                .catch(error => addResult('✗ 创建角色失败: ' + error, false));
        }

        function testHandlers() {
            fetch('/api/test_handlers')
                .then(response => response.json())
                .then(data => {
                    addResult('✓ ' + data.message, data.success);
                    if (data.details) {
                        data.details.forEach(detail => addResult('  - ' + detail, true));
                    }
                })
                .catch(error => addResult('✗ Handler测试失败: ' + error, false));
        }

        function runPerformanceTest() {
            fetch('/api/performance_test')
                .then(response => response.json())
                .then(data => {
                    addResult('✓ ' + data.message, data.success);
                    if (data.performance) {
                        addResult('  性能数据: ' + JSON.stringify(data.performance), true);
                    }
                })
                .catch(error => addResult('✗ 性能测试失败: ' + error, false));
        }

        function runCleanupTest() {
            fetch('/api/cleanup_test')
                .then(response => response.json())
                .then(data => {
                    addResult('✓ ' + data.message, data.success);
                    if (data.cleaned_count !== undefined) {
                        addResult('  清理了 ' + data.cleaned_count + ' 个Handler', true);
                    }
                })
                .catch(error => addResult('✗ 清理测试失败: ' + error, false));
        }

        function refreshStats() {
            // 获取内存统计
            fetch('/api/memory_stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('memory-stats').innerHTML = 
                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                });

            // 获取事件历史
            fetch('/api/event_history')
                .then(response => response.json())
                .then(data => {
                    const eventList = data.events.slice(0, 5).map(event => 
                        event.event_type + ' - ' + event.source_handler
                    ).join('<br>');
                    document.getElementById('event-history').innerHTML = 
                        eventList || '暂无事件';
                });
        }

        // 页面加载时自动刷新统计
        window.onload = function() {
            refreshStats();
        };
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def create_test_character(self, query):
        """创建测试角色"""
        try:
            name = query.get('name', ['测试角色'])[0]
            char = TestCharacter(name)
            test_characters[char.id] = char
            
            response = {
                "success": True,
                "message": f"成功创建角色: {name} (ID: {char.id})",
                "character_id": char.id
            }
        except Exception as e:
            response = {
                "success": False,
                "message": f"创建角色失败: {str(e)}"
            }
        
        self.send_json_response(response)
    
    def test_handlers(self, query):
        """测试Handler功能"""
        try:
            if not test_characters:
                # 创建一个测试角色
                char = TestCharacter("自动创建角色")
                test_characters[char.id] = char
            
            char = list(test_characters.values())[0]
            details = []
            
            # 测试修仙Handler
            cultivation = char.cultivation
            cultivation.advance_cultivation(100)
            details.append("修仙Handler测试通过")
            
            # 测试因果Handler
            karma = char.karma
            karma.record_good_deed("测试善行", 10)
            details.append("因果Handler测试通过")
            
            # 测试AI导演Handler
            try:
                ai_director = char.ai_director
                ai_director.update_context("测试上下文")
                details.append("AI导演Handler测试通过")
            except Exception as e:
                details.append(f"AI导演Handler测试跳过: {str(e)}")
            
            response = {
                "success": True,
                "message": "Handler功能测试完成",
                "details": details
            }
        except Exception as e:
            response = {
                "success": False,
                "message": f"Handler测试失败: {str(e)}"
            }
        
        self.send_json_response(response)
    
    def get_memory_stats(self):
        """获取内存统计"""
        try:
            stats = HandlerMemoryManager.get_optimization_report()
            self.send_json_response(stats)
        except Exception as e:
            self.send_json_response({"error": str(e)})
    
    def get_event_history(self):
        """获取事件历史"""
        try:
            history = HandlerCommunicationBus.get_event_history()
            events = []
            for event in history[-10:]:  # 最近10个事件
                events.append({
                    "event_type": event.event_type.value,
                    "source_handler": event.source_handler,
                    "target_handler": event.target_handler,
                    "timestamp": event.timestamp
                })
            
            self.send_json_response({"events": events})
        except Exception as e:
            self.send_json_response({"error": str(e)})
    
    def run_performance_test(self):
        """运行性能测试"""
        try:
            # 创建多个角色进行性能测试
            start_time = time.time()
            
            for i in range(10):
                char = TestCharacter(f"性能测试角色{i}")
                test_characters[char.id] = char
                # 访问Handler
                char.cultivation.advance_cultivation(i * 10)
                char.karma.record_good_deed("性能测试", i)
            
            creation_time = time.time() - start_time
            
            # 测试访问性能
            start_time = time.time()
            for char in list(test_characters.values())[-10:]:
                for _ in range(100):
                    _ = char.cultivation
                    _ = char.karma
            
            access_time = time.time() - start_time
            
            performance = {
                "creation_time": f"{creation_time:.4f}秒",
                "access_time": f"{access_time:.4f}秒",
                "characters_created": 10,
                "total_accesses": 2000
            }
            
            response = {
                "success": True,
                "message": "性能测试完成",
                "performance": performance
            }
        except Exception as e:
            response = {
                "success": False,
                "message": f"性能测试失败: {str(e)}"
            }
        
        self.send_json_response(response)
    
    def run_cleanup_test(self):
        """运行清理测试"""
        try:
            # 强制清理所有Handler
            cleaned_count = HandlerMemoryManager.cleanup_inactive_handlers(max_idle_time=0)
            
            response = {
                "success": True,
                "message": "内存清理测试完成",
                "cleaned_count": cleaned_count
            }
        except Exception as e:
            response = {
                "success": False,
                "message": f"清理测试失败: {str(e)}"
            }
        
        self.send_json_response(response)
    
    def send_json_response(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))


def start_test_server(port=8000):
    """启动测试服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, TestWebHandler)
    print(f"🚀 Handler生态测试服务器启动在 http://localhost:{port}")
    print("📊 访问 http://localhost:8000 进行Web测试")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()


if __name__ == "__main__":
    start_test_server()
