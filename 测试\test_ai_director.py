#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统测试脚本
测试故事大纲解析、决策生成和性能
"""

import os
import sys
import time
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')

try:
    import django
    django.setup()
except:
    print("Django setup skipped")

from systems.ai_director import AIDirector, StoryPhase, DecisionType
from systems.event_system import BaseEvent, EventPriority


class TestEvent(BaseEvent):
    """测试用事件"""
    def __init__(self, event_type="TestEvent", description="测试事件"):
        super().__init__()
        self.event_type = event_type
        self.description = description
        self.character_name = "测试角色"
        self.location = "测试地点"
        self.priority = EventPriority.NORMAL


def test_story_outline_parsing():
    """测试故事大纲解析功能"""
    print("\n" + "="*60)
    print("测试1: 故事大纲解析")
    print("="*60)
    
    ai_director = AIDirector()
    
    # 测试故事大纲
    story_outline = """
    《逆天改命》
    
    主题：凡人逆天修仙，挑战命运束缚
    
    核心冲突：主角作为废灵根的凡人，在修仙界备受歧视，但通过特殊机缘获得了改变命运的机会，
    与各大宗门、天道意志产生冲突。
    
    主要角色：
    - 林逸风：主角，废灵根却有惊人悟性
    - 苏清雪：青云宗天才弟子，主角的引路人
    - 魔君血煞：上古魔头，与主角有因果纠葛
    - 天机老人：神秘高人，掌握改命之法
    
    剧情要点：
    1. 序章：林逸风在凡人村庄遭遇妖兽袭击，意外觉醒特殊体质
    2. 起承：拜入青云宗外门，受尽欺凌但坚持修炼
    3. 转折：秘境中获得上古传承，实力突飞猛进
    4. 高潮：各方势力争夺主角身上的秘密，大战爆发
    5. 结局：突破天道束缚，开辟新的修炼之路
    """
    
    try:
        outline = ai_director.analyze_story_outline(story_outline)
        
        print(f"✅ 解析成功!")
        print(f"   标题: {outline.title}")
        print(f"   主题: {outline.theme}")
        print(f"   核心冲突: {outline.main_conflict}")
        print(f"   关键角色: {', '.join(outline.key_characters)}")
        print(f"   剧情点数: {len(outline.major_plot_points)}")
        print(f"   故事阶段: {[phase.value for phase in outline.expected_phases]}")
        
        # 设置为活跃大纲
        ai_director.set_active_outline(outline.outline_id)
        
        return True
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return False


def test_ai_decision_making():
    """测试AI决策生成"""
    print("\n" + "="*60)
    print("测试2: AI决策生成")
    print("="*60)
    
    ai_director = AIDirector()
    
    # 测试不同类型的事件
    test_events = [
        {
            "event_type": "CultivationBreakthroughEvent",
            "description": "林逸风成功突破到筑基期，引起天地异象",
            "character": "林逸风",
            "location": "青云峰",
            "priority": "HIGH"
        },
        {
            "event_type": "CombatEvent", 
            "description": "林逸风与内门弟子发生冲突，展现惊人实力",
            "character": "林逸风",
            "location": "演武场",
            "priority": "NORMAL"
        },
        {
            "event_type": "DiscoveryEvent",
            "description": "在后山发现一处隐秘洞府，似有宝物",
            "character": "林逸风", 
            "location": "青云后山",
            "priority": "HIGH"
        }
    ]
    
    all_success = True
    total_time = 0
    
    for i, event_data in enumerate(test_events, 1):
        print(f"\n测试事件 {i}: {event_data['event_type']}")
        
        try:
            start_time = time.time()
            decision = ai_director.make_decision(event_data)
            
            print(f"✅ 决策生成成功!")
            print(f"   决策类型: {decision.decision_type.value}")
            print(f"   决策内容: {decision.content[:100]}...")
            print(f"   置信度: {decision.confidence:.2f}")
            print(f"   响应时间: {decision.response_time:.3f}秒")
            
            total_time += decision.response_time
            
            # 检查是否有后续行动建议
            next_actions = decision.context.get("next_actions", [])
            if next_actions:
                print(f"   后续建议: {', '.join(next_actions[:3])}")
            
        except Exception as e:
            print(f"❌ 决策生成失败: {e}")
            all_success = False
    
    if all_success:
        avg_time = total_time / len(test_events)
        print(f"\n📊 平均响应时间: {avg_time:.3f}秒")
    
    return all_success


def test_story_state_tracking():
    """测试剧情状态追踪"""
    print("\n" + "="*60) 
    print("测试3: 剧情状态追踪")
    print("="*60)
    
    ai_director = AIDirector()
    
    # 先解析一个故事大纲
    outline_text = "简单测试故事：主角修炼、遇险、获得机缘、最终飞升"
    outline = ai_director.analyze_story_outline(outline_text)
    ai_director.set_active_outline(outline.outline_id)
    
    # 模拟一系列事件来推进故事
    events_sequence = [
        {"event_type": "GameStart", "description": "游戏开始", "phase": "序章"},
        {"event_type": "FirstCultivation", "description": "第一次修炼", "phase": "序章"},
        {"event_type": "MeetMentor", "description": "遇到导师", "phase": "起承"},
        {"event_type": "FirstBattle", "description": "第一次战斗", "phase": "起承"},
        {"event_type": "BigChallenge", "description": "重大挑战", "phase": "高潮"},
        {"event_type": "FinalBattle", "description": "最终战斗", "phase": "高潮"}
    ]
    
    print(f"初始故事阶段: {ai_director.get_story_state().current_phase.value}")
    
    for event in events_sequence:
        # 生成决策
        decision = ai_director.make_decision(event)
        
        # 获取更新后的状态
        state = ai_director.get_story_state()
        
        print(f"\n事件: {event['description']}")
        print(f"  当前阶段: {state.current_phase.value}")
        print(f"  决策历史: {len(list(state.decision_history))}")
        print(f"  世界变化: {len(state.world_changes)}")
    
    # 检查性能统计
    stats = ai_director.get_performance_stats()
    print(f"\n📊 性能统计:")
    print(f"   总决策数: {stats['total_decisions']}")
    print(f"   平均响应时间: {stats['average_response_time']:.3f}秒")
    print(f"   缓存命中: {stats['cache_hits']}")
    print(f"   缓存未命中: {stats['cache_misses']}")
    
    return True


def test_performance():
    """测试性能要求：响应时间 < 200ms"""
    print("\n" + "="*60)
    print("测试4: 性能测试")
    print("="*60)
    
    ai_director = AIDirector()
    
    # 预热：第一次调用通常较慢
    warmup_event = {
        "event_type": "WarmupEvent",
        "description": "预热事件",
        "character": "测试",
        "location": "测试"
    }
    ai_director.make_decision(warmup_event)
    
    # 测试多次调用的性能
    test_count = 10
    response_times = []
    
    print(f"执行 {test_count} 次决策生成测试...")
    
    for i in range(test_count):
        event_data = {
            "event_type": f"TestEvent_{i}",
            "description": f"测试事件 {i}",
            "character": "测试角色",
            "location": "测试地点",
            "timestamp": time.time()
        }
        
        decision = ai_director.make_decision(event_data)
        response_times.append(decision.response_time)
        
        # 显示进度
        print(f".", end="", flush=True)
    
    print("\n")
    
    # 分析结果
    avg_time = sum(response_times) / len(response_times)
    max_time = max(response_times)
    min_time = min(response_times)
    under_200ms = sum(1 for t in response_times if t < 0.2)
    
    print(f"📊 性能测试结果:")
    print(f"   平均响应时间: {avg_time*1000:.1f}ms")
    print(f"   最快响应时间: {min_time*1000:.1f}ms")
    print(f"   最慢响应时间: {max_time*1000:.1f}ms")
    print(f"   <200ms 的比例: {under_200ms}/{test_count} ({under_200ms/test_count*100:.1f}%)")
    
    # 测试缓存效果
    print(f"\n测试缓存效果...")
    cached_event = {
        "event_type": "CachedEvent",
        "description": "缓存测试事件",
        "character": "测试角色",
        "location": "测试地点"
    }
    
    # 第一次调用
    decision1 = ai_director.make_decision(cached_event)
    time1 = decision1.response_time
    
    # 第二次调用（应该命中缓存）
    decision2 = ai_director.make_decision(cached_event)
    time2 = decision2.response_time
    
    print(f"   第一次调用: {time1*1000:.1f}ms")
    print(f"   第二次调用(缓存): {time2*1000:.1f}ms")
    print(f"   加速比: {time1/time2:.1f}x")
    
    success = avg_time < 0.2  # 平均时间小于200ms
    print(f"\n{'✅' if success else '❌'} 性能测试{'通过' if success else '未通过'}")
    
    return success


def main():
    """运行所有测试"""
    print("🧪 AI导演系统测试")
    print(f"   时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("故事大纲解析", test_story_outline_parsing),
        ("AI决策生成", test_ai_decision_making),
        ("剧情状态追踪", test_story_state_tracking),
        ("性能测试", test_performance)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"\n❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过!")
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败")


if __name__ == "__main__":
    main()