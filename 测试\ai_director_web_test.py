#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统Web页面测试
使用Python 3.12测试实际的Evennia web界面和API
"""

import requests
import time
import json
import sys
from datetime import datetime

class AIDirectorWebTest:
    """AI导演系统Web页面测试"""
    
    def __init__(self):
        self.base_url = "http://localhost:4001"  # Evennia web端口
        self.api_base = f"{self.base_url}/api/xiuxian"
        self.test_results = []
        self.session = requests.Session()
        
        print("🌐 AI导演系统Web页面测试")
        print("🎮 测试实际的mygame仙侠MUD web界面")
        print("="*60)
        
    def test_web_server_connection(self):
        """测试Web服务器连接"""
        print("\n🔗 测试Web服务器连接...")
        try:
            response = self.session.get(self.base_url, timeout=10)
            if response.status_code == 200:
                print("✅ Web服务器连接成功")
                print(f"   状态码: {response.status_code}")
                print(f"   响应大小: {len(response.content)} bytes")
                
                # 检查是否是Evennia页面
                if "evennia" in response.text.lower() or "mygame" in response.text.lower():
                    print("✅ 确认为Evennia游戏页面")
                else:
                    print("⚠️ 页面内容可能不是Evennia")
                
                self.test_results.append({
                    "test": "Web服务器连接",
                    "status": "成功",
                    "status_code": response.status_code,
                    "content_length": len(response.content)
                })
                return True
            else:
                print(f"❌ Web服务器响应异常，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Web服务器连接失败: {e}")
            return False
    
    def test_webclient_access(self):
        """测试Web客户端访问"""
        print("\n🎮 测试Web客户端访问...")
        try:
            webclient_url = f"{self.base_url}/webclient/"
            response = self.session.get(webclient_url, timeout=10)
            
            if response.status_code == 200:
                print("✅ Web客户端页面访问成功")
                
                # 检查关键元素
                content = response.text.lower()
                checks = [
                    ("websocket", "WebSocket支持"),
                    ("connect", "连接功能"),
                    ("login", "登录功能"),
                    ("javascript", "JavaScript支持")
                ]
                
                for keyword, description in checks:
                    if keyword in content:
                        print(f"   ✅ {description}: 检测到")
                    else:
                        print(f"   ⚠️ {description}: 未检测到")
                
                self.test_results.append({
                    "test": "Web客户端访问",
                    "status": "成功",
                    "url": webclient_url,
                    "features_detected": [desc for kw, desc in checks if kw in content]
                })
                return True
            else:
                print(f"❌ Web客户端访问失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Web客户端访问异常: {e}")
            return False
    
    def test_api_endpoints(self):
        """测试API端点"""
        print("\n🔌 测试AI导演API端点...")
        
        api_endpoints = [
            ("GET", "/ai-director/story-status/", "获取故事状态"),
            ("GET", "/ai-director/world-state/", "获取世界状态"),
            ("GET", "/cultivation/realm/", "获取修炼境界"),
            ("GET", "/karma/status/", "获取因果状态")
        ]
        
        success_count = 0
        for method, endpoint, description in api_endpoints:
            try:
                url = f"{self.api_base}{endpoint}"
                print(f"\n📡 测试: {description}")
                print(f"   URL: {url}")
                
                if method == "GET":
                    response = self.session.get(url, timeout=10)
                else:
                    response = self.session.post(url, timeout=10)
                
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("   ✅ API调用成功")
                    try:
                        data = response.json()
                        print(f"   📄 响应数据: {json.dumps(data, ensure_ascii=False, indent=2)[:200]}...")
                        success_count += 1
                    except:
                        print("   ⚠️ 响应不是JSON格式")
                        print(f"   📄 响应内容: {response.text[:200]}...")
                elif response.status_code == 404:
                    print("   ⚠️ API端点不存在 (404)")
                elif response.status_code == 500:
                    print("   ❌ 服务器内部错误 (500)")
                else:
                    print(f"   ⚠️ 未预期的状态码: {response.status_code}")
                
                self.test_results.append({
                    "test": f"API - {description}",
                    "status": "成功" if response.status_code == 200 else "失败",
                    "status_code": response.status_code,
                    "endpoint": endpoint,
                    "method": method
                })
                
            except Exception as e:
                print(f"   ❌ API测试异常: {e}")
                self.test_results.append({
                    "test": f"API - {description}",
                    "status": "异常",
                    "error": str(e)
                })
        
        print(f"\n📊 API测试总结: {success_count}/{len(api_endpoints)} 个端点成功")
        return success_count > 0
    
    def test_ai_director_integration(self):
        """测试AI导演系统集成"""
        print("\n🎭 测试AI导演系统集成...")
        
        try:
            # 测试更新上下文API
            context_url = f"{self.api_base}/ai-director/update-context/"
            test_context = {
                "context": "玩家林逸风开始修炼九转玄功，天地灵气汇聚"
            }
            
            print("📝 测试上下文更新...")
            response = self.session.post(
                context_url,
                json=test_context,
                headers={'Content-Type': 'application/json'},
                timeout=15
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 上下文更新成功")
                try:
                    data = response.json()
                    print(f"   🎭 AI响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    
                    # 检查AI响应内容
                    if 'success' in data and data.get('success'):
                        print("   ✅ AI导演系统响应正常")
                        if 'ai_response' in data:
                            ai_content = data['ai_response']
                            print(f"   🤖 AI生成内容: {ai_content[:100]}...")
                    else:
                        print("   ⚠️ AI导演系统响应异常")
                        
                except Exception as e:
                    print(f"   ⚠️ 响应解析失败: {e}")
                    print(f"   📄 原始响应: {response.text[:200]}...")
                
                self.test_results.append({
                    "test": "AI导演系统集成",
                    "status": "成功",
                    "context_sent": test_context["context"],
                    "response_received": True
                })
                return True
            else:
                print(f"   ❌ 上下文更新失败，状态码: {response.status_code}")
                print(f"   📄 错误响应: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ AI导演集成测试异常: {e}")
            return False
    
    def test_performance_monitoring(self):
        """测试性能监控"""
        print("\n📊 测试性能监控...")
        
        start_time = time.time()
        response_times = []
        
        # 进行多次API调用测试性能
        test_urls = [
            f"{self.api_base}/cultivation/realm/",
            f"{self.api_base}/ai-director/story-status/",
            f"{self.api_base}/karma/status/"
        ]
        
        for i in range(3):
            for url in test_urls:
                try:
                    req_start = time.time()
                    response = self.session.get(url, timeout=5)
                    req_time = time.time() - req_start
                    response_times.append(req_time)
                    
                    print(f"   📡 请求 {len(response_times)}: {req_time:.3f}秒 (状态码: {response.status_code})")
                    
                except Exception as e:
                    print(f"   ❌ 请求失败: {e}")
        
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            
            print(f"\n📈 性能统计:")
            print(f"   平均响应时间: {avg_time:.3f}秒")
            print(f"   最快响应时间: {min_time:.3f}秒")
            print(f"   最慢响应时间: {max_time:.3f}秒")
            print(f"   总请求数: {len(response_times)}")
            
            # 性能评级
            if avg_time < 0.1:
                rating = "优秀"
                emoji = "🟢"
            elif avg_time < 0.5:
                rating = "良好"
                emoji = "🟡"
            else:
                rating = "需优化"
                emoji = "🟠"
            
            print(f"   {emoji} 性能评级: {rating}")
            
            self.test_results.append({
                "test": "性能监控",
                "status": "成功",
                "avg_response_time": avg_time,
                "max_response_time": max_time,
                "min_response_time": min_time,
                "total_requests": len(response_times),
                "rating": rating
            })
            return True
        else:
            print("❌ 无法获取性能数据")
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 AI导演系统Web测试报告")
        print("="*60)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r["status"] == "成功"])
        failed_tests = total_tests - successful_tests
        
        print(f"🕒 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🐍 Python版本: {sys.version}")
        print(f"🌐 测试目标: {self.base_url}")
        print(f"📊 总测试数: {total_tests}")
        print(f"✅ 成功: {successful_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"📈 成功率: {(successful_tests/total_tests*100):.1f}%")
        
        print("\n📋 详细结果:")
        for i, result in enumerate(self.test_results, 1):
            status_icon = "✅" if result["status"] == "成功" else "❌"
            print(f"{i:2d}. {status_icon} {result['test']}: {result['status']}")
            
            if "error" in result:
                print(f"     错误: {result['error']}")
            elif "status_code" in result:
                print(f"     状态码: {result['status_code']}")
        
        # 保存详细报告
        report_file = "测试/ai_director_web_test_report.json"
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "python_version": sys.version,
                "target_url": self.base_url,
                "summary": {
                    "total": total_tests,
                    "successful": successful_tests,
                    "failed": failed_tests,
                    "success_rate": (successful_tests/total_tests*100)
                },
                "results": self.test_results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        if failed_tests == 0:
            print("\n🎉 所有测试通过！AI导演系统Web界面功能正常！")
        else:
            print(f"\n⚠️ 有 {failed_tests} 个测试失败，请检查相关功能。")
    
    def run_full_web_test(self):
        """运行完整Web测试"""
        print("🚀 开始AI导演系统Web页面测试...")
        print("🎮 使用Python 3.12测试实际的mygame环境")
        print("="*60)
        
        # 运行各项测试
        tests = [
            ("Web服务器连接", self.test_web_server_connection),
            ("Web客户端访问", self.test_webclient_access),
            ("API端点测试", self.test_api_endpoints),
            ("AI导演集成", self.test_ai_director_integration),
            ("性能监控", self.test_performance_monitoring)
        ]
        
        for test_name, test_func in tests:
            try:
                print(f"\n🧪 执行测试: {test_name}")
                test_func()
                time.sleep(1)  # 测试间隔
            except Exception as e:
                print(f"❌ 测试 '{test_name}' 执行异常: {e}")
                self.test_results.append({
                    "test": test_name,
                    "status": "异常",
                    "error": str(e)
                })
        
        # 生成测试报告
        self.generate_test_report()

def main():
    """主函数"""
    print("🧙‍♂️ AI导演系统Web页面测试启动...")
    print("确保Evennia服务器正在运行...")
    print("测试将在3秒后开始...")
    time.sleep(3)
    
    tester = AIDirectorWebTest()
    tester.run_full_web_test()

if __name__ == "__main__":
    main()
